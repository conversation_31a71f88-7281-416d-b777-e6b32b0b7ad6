
import 'dart:io';

import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/http.dart';

import '../common/config/net/dio.dart';

part 'global_api.g.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/11/12
///   desc   : retrofit
///   version: 1.0

@RestApi()
abstract class GlobalApi {
  static GlobalApi? _instance;

  factory GlobalApi.instance() {
    return GlobalApi();
  }

  factory GlobalApi() {
    _instance ??= _GlobalApi(dio);
    return _instance!;
  }

}
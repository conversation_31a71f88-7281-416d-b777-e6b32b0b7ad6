import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言俄语
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringRu extends GlobalString {

  @override
  String cancel="Отмена";
  
  @override
  String confirm="Подтвердить";
  
  @override
  String delete='Удалить';

  //home
  @override
  String untitled='Без названия';
  
  @override
  String welcomeToUse='Добро пожаловать в ';
  
  @override
  String confirmToDelete='Подтвердить удаление?';
  
  @override
  String renameDrawing='Переименовать';
  
  @override
  String nameCouldNotEmpty='Имя не может быть пустым!';

  //drawing_board
  @override
  String onSaving='Сохранение...';

  @override
  String handWriting='рукописный ввод';

  @override
  String strokeWidth="толщина линии";
  
  @override
  String appName="Мастер Доски";
}

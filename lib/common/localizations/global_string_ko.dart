import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言韩语
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringKo extends GlobalString {

  @override
  String cancel="취소";
  
  @override
  String confirm="확인";
  
  @override
  String delete='삭제';

  //home
  @override
  String untitled='제목 없음';
  
  @override
  String welcomeToUse='환영합니다 ';
  
  @override
  String confirmToDelete='삭제를 확인하시겠습니까?';
  
  @override
  String renameDrawing='이름 바꾸기';
  
  @override
  String nameCouldNotEmpty='이름은 비워둘 수 없습니다!';

  //drawing_board
  @override
  String onSaving='저장 중...';

  @override
  String handWriting='손글씨';

  @override
  String strokeWidth="선 두께";
  
  @override
  String appName="화이트보드 마스터";

  @override
  String languageSettings="언어 설정";

  @override
  String selectLanguage="언어 선택";

  @override
  String currentLanguage="현재 언어";
}

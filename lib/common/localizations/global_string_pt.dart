import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言葡萄牙语
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringPt extends GlobalString {

  @override
  String cancel="Cancelar";
  
  @override
  String confirm="Confirmar";
  
  @override
  String delete='Excluir';

  //home
  @override
  String untitled='Sem título';
  
  @override
  String welcomeToUse='Bem-vindo ao ';
  
  @override
  String confirmToDelete='Confirmar exclusão?';
  
  @override
  String renameDrawing='Renomear';
  
  @override
  String nameCouldNotEmpty='O nome não pode estar vazio!';

  //drawing_board
  @override
  String onSaving='Salvando...';

  @override
  String handWriting='escrita à mão';

  @override
  String strokeWidth="largura do traço";
  
  @override
  String appName="Mestre do Quadro Branco";

  @override
  String languageSettings="Configurações de idioma";

  @override
  String selectLanguage="Selecionar idioma";

  @override
  String currentLanguage="Idioma atual";
}

import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言日语
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringJa extends GlobalString {

  @override
  String cancel="キャンセル";
  
  @override
  String confirm="確認";
  
  @override
  String delete='削除';

  //home
  @override
  String untitled='無題';
  
  @override
  String welcomeToUse='ようこそ ';
  
  @override
  String confirmToDelete='削除を確認しますか？';
  
  @override
  String renameDrawing='名前を変更';
  
  @override
  String nameCouldNotEmpty='名前を空にすることはできません！';

  //drawing_board
  @override
  String onSaving='保存中...';

  @override
  String handWriting='手書き';

  @override
  String strokeWidth="線の太さ";
  
  @override
  String appName="ホワイトボードマスター";

  @override
  String languageSettings="言語設定";

  @override
  String selectLanguage="言語を選択";

  @override
  String currentLanguage="現在の言語";
}

import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言西班牙语
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringEs extends GlobalString {

  @override
  String cancel="Cancelar";
  
  @override
  String confirm="Confirmar";
  
  @override
  String delete='Eliminar';

  //home
  @override
  String untitled='Sin título';
  
  @override
  String welcomeToUse='Bienvenido a ';
  
  @override
  String confirmToDelete='¿Confirmar eliminación?';
  
  @override
  String renameDrawing='Renombrar';
  
  @override
  String nameCouldNotEmpty='¡El nombre no puede estar vacío!';

  //drawing_board
  @override
  String onSaving='Guardando...';

  @override
  String handWriting='escritura a mano';

  @override
  String strokeWidth="ancho de trazo";
  
  @override
  String appName="Maestro de Pizarra";

  @override
  String languageSettings="Configuración de idioma";

  @override
  String selectLanguage="Seleccionar idioma";

  @override
  String currentLanguage="Idioma actual";
}

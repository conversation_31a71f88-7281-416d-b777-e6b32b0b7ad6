import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'global_localizations.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言代理
///   version: 1.0
class GlobalLocalizationsDelegate extends LocalizationsDelegate<GlobalLocalizations> {

  GlobalLocalizationsDelegate();

  ///支持的语言 中文、英语、西班牙语、葡萄牙语、俄语、阿拉伯语、日语、韩语
  var supportedLocales =  [
    const Locale('zh', 'CH'),
    const Locale('en', 'US'),
    const Locale('es', 'ES'),
    const Locale('pt', 'PT'),
    const Locale('ru', 'RU'),
    const Locale('ar', 'SA'),
    const Locale('ja', 'JP'),
    const Locale('ko', 'KR'),
  ];

  @override
  bool isSupported(Locale locale) {
    return supportedLocales.any((element) => element.languageCode == locale.languageCode);
  }

  ///根据locale，创建一个对象用于提供当前locale下的文本显示
  @override
  Future<GlobalLocalizations> load(Locale locale) {
    return SynchronousFuture<GlobalLocalizations>(GlobalLocalizations(locale));
  }

  @override
  bool shouldReload(LocalizationsDelegate<GlobalLocalizations> old) => false;

  /// 获取当前语言
  String getCurrentLanguageCode(BuildContext context) {
    return Localizations.localeOf(context).languageCode;
  }

  ///全局静态的代理
  static   GlobalLocalizationsDelegate delegate = GlobalLocalizationsDelegate();
}

import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言阿拉伯语(沙特)
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
class GlobalStringAr extends GlobalString {

  @override
  String cancel="إلغاء";
  
  @override
  String confirm="تأكيد";
  
  @override
  String delete='حذف';

  //home
  @override
  String untitled='بدون عنوان';
  
  @override
  String welcomeToUse='مرحباً بك في ';
  
  @override
  String confirmToDelete='تأكيد الحذف؟';
  
  @override
  String renameDrawing='إعادة تسمية';
  
  @override
  String nameCouldNotEmpty='لا يمكن أن يكون الاسم فارغاً!';

  //drawing_board
  @override
  String onSaving='جاري الحفظ...';

  @override
  String handWriting='الكتابة اليدوية';

  @override
  String strokeWidth="عرض الخط";
  
  @override
  String appName="أستاذ السبورة البيضاء";

  @override
  String languageSettings="إعدادات اللغة";

  @override
  String selectLanguage="اختيار اللغة";

  @override
  String currentLanguage="اللغة الحالية";
}

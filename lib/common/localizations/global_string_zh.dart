
import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言英文
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
// class GlobalStringEn implements GlobalStringZh {
class GlobalStringZh extends GlobalString {



  String cancel="取消";
  String confirm="确定";
  String delete='删除';

  //home
  String untitled='未命名';
  String welcomeToUse='欢迎来到';
  String confirmToDelete='确定要删除吗？';
  String renameDrawing='重命名';
  String nameCouldNotEmpty='名称不能为空';

  //drawing_board

  String onSaving='保存中...';

  String handWriting='手写';


  String strokeWidth="笔画宽度";
  String appName="白板大师";
}


import 'global_string.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言英文
///   version: 1.0

///需要国际化的时候使用implements然后重写属性
// class GlobalStringEn implements GlobalStringZh {
class GlobalStringZh extends GlobalString {



  @override
  String cancel="取消";

  @override
  String confirm="确定";

  @override
  String delete='删除';

  //home
  @override
  String untitled='未命名';

  @override
  String welcomeToUse='欢迎来到';

  @override
  String confirmToDelete='确定要删除吗？';

  @override
  String renameDrawing='重命名';

  @override
  String nameCouldNotEmpty='名称不能为空';

  //drawing_board
  @override
  String onSaving='保存中...';

  @override
  String handWriting='手写';

  @override
  String strokeWidth="笔画宽度";

  @override
  String appName="白板大师";

  @override
  String languageSettings="语言设置";

  @override
  String selectLanguage="选择语言";

  @override
  String currentLanguage="当前语言";
}

import 'package:flutter/material.dart';

import 'global_string.dart';
import 'global_string_zh.dart';
import 'global_string_es.dart';
import 'global_string_pt.dart';
import 'global_string_ru.dart';
import 'global_string_ar.dart';
import 'global_string_ja.dart';
import 'global_string_ko.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 自定义多语言实现
///   version: 1.0
class GlobalLocalizations {
  final Locale locale;

  GlobalLocalizations(this.locale);

  ///根据不同 locale.languageCode 加载不同语言对应
  ///GlobalString默认为英文,其他语言需要重写属性
  static final Map<String, GlobalString> _localizedValues = {
    'en': GlobalString(),
    'zh': GlobalStringZh(),
    'es': GlobalStringEs(),
    'pt': GlobalStringPt(),
    'ru': GlobalStringRu(),
    'ar': GlobalStringAr(),
    'ja': GlobalStringJa(),
    'ko': GlobalStringKo(),
  };

  GlobalString get currentLocalized {
    if (_localizedValues.containsKey(locale.languageCode)) {
      return _localizedValues[locale.languageCode]!;
    }
    return _localizedValues["en"]!;
  }

  ///通过 Localizations 加载当前的 GSYLocalizations
  ///获取对应的 GSYStringBase
  static GlobalString i18n(BuildContext context) {
    return Localizations.of<GlobalLocalizations>(context, GlobalLocalizations)!
        .currentLocalized;
  }
}

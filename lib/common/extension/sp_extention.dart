


import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';


extension SpExtendion on SharedPreferences{


  Future<bool> setJson(String key, dynamic value){
    String jsonStr=jsonEncode(value);
    return setString( key, jsonStr);
  }

  Map<String,dynamic>?  getJson(String key){
    String? value=getString(key);
    if(value!=null){
      Map<String,dynamic>? json= jsonDecode(value);
      return json;
    }
  }
}
import 'package:easy_white_board/common/config/application.dart';
import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/common/localizations/global_string.dart';
import 'package:flutter/foundation.dart';

import 'env/env_config.dart';

class BuildConfig {
  /// 是否是生产环境
  static const bool isProduction = kReleaseMode;

  ///环境信息 如:debug test release
  static final EnvConfig envConfig = Env.envConfig;

  static late String versionName;
  static late String buildNumber;
  static String appName = GlobalString().appName;
  static late String packageName;
  static late String channelName;
  static late String sdkVersion;
}

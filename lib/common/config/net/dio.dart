



import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';

import '../build_config.dart';
import 'interceptors/header_interceptor.dart';
import 'interceptors/loading_interceptor.dart';
import 'interceptors/log_interceptor.dart';
import 'interceptors/token_interceptor.dart';




Dio initDioAdapter(){
  Dio mdio=Dio();
  mdio.httpClientAdapter=IOHttpClientAdapter();
  (mdio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate = (client) {
    client.badCertificateCallback = (cert, host, port) {
      return true;
    };
  };
  return mdio;
}

final Dio dio = initDioAdapter()

///设置baseUrl
  ..options.baseUrl = BuildConfig.envConfig.domain

///默认contentType
  ..options.contentType = ContentType.json.toString()

///超时设置
  ..options.sendTimeout = const Duration(seconds: 45)
  ..options.receiveTimeout = const Duration(seconds: 45)
  ..options.connectTimeout = const Duration(seconds: 45)

///错误后不解析response,避免出现格式解析错误
  ..options.receiveDataWhenStatusError = false


  ..interceptors.add(HeaderInterceptor())
  ..interceptors.add(TokenInterceptor())
  ..interceptors.add(CustomLogInterceptor())
  ..interceptors.add(LoadingInterceptor())
;




import 'package:json_annotation/json_annotation.dart';

import 'business_code.dart';

part 'base_response.g.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/11/18
///   desc   : 网络响应基类
///   version: 1.0
@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  T? data;
  int code;
  String? msg;
  bool? showLocalTab;
  //客服  ENTERPRISE_WECHAT  企业微信，  HUAN_XIN 环信
  String? customerType;

  BaseResponse(this.data, this.code,this.showLocalTab,this.msg);

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseResponseFromJson(json, fromJsonT);

  ///是否成功
  bool get isSuccess => code == BusinessCode.success;

  ///是否失败
  bool get isFailure => !isSuccess;

  ///数据是否为空
  bool get isDataNull => data == null;

  ///数据是否为非空
  bool get isDataNotNull => data != null;

  ///获取非空的数据
  T get dataNotNull => data!;

  ///成功并且数据不为空
  BaseResponse<T> onSuccess(void Function(T value) action) {
    if (isSuccess && isDataNotNull) action(dataNotNull);
    return this;
  }

  ///成功并且数据可空
  BaseResponse<T> onSuccessNullable(void Function(T? value) action) {
    if (isSuccess) action(data);
    return this;
  }

  ///失败
  BaseResponse<T> onFailure(void Function(int code, String? msg) action) {
    if (isFailure) action(code, msg);
    return this;
  }

  R requireData<R>(R Function(T? value) onSuccess,
      [R Function(int code, String? msg)? onFailure]) {
    if (isSuccess) return onSuccess(data);
    if (onFailure != null) {
      return onFailure(code, msg);
    }
    throw Exception(msg);
  }
}

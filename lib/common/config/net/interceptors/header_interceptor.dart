import 'dart:io';

import 'package:dio/dio.dart';

import '../../../../resource/shared_preferences_keys.dart';
import '../../build_config.dart';
import '../../storage_manager.dart';

Map<String, dynamic> optHeader = {
  'accept-language': 'zh-cn',
  'content-type': 'application/json'
};
class HeaderInterceptor extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {

    options.headers=optHeader;
    options.headers['version'] = BuildConfig.buildNumber;
    options.headers['platform'] = Platform.operatingSystem;
    super.onRequest(options, handler);
  }
}





import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../log/log.dart';
import '../../../../api/api.dart';


class CustomLogInterceptor extends InterceptorsWrapper {

  ///是否不打印request参数
  static const String ignoreRequestParam = "printRequestParam";
  //是否不打印response参数
  static const String ignoreResponseLog = "printResponse";


  // final List<String> _noLogResponseList = [
  //     Api.getKaptchaImage,
  //   Api.getCaptcha,
  // ];

  bool urlMatches(List<String> list,String path){
    for (String element in list){
      if(element.startsWith("/")){
        element=element.substring(1);
      }
      if(element.endsWith("/")){
        element=element.substring(0,element.length-1);
      }
      if(element==path){
        return true;
      }
    }
    return false;
  }

  @override
  onRequest(
      RequestOptions options,
      RequestInterceptorHandler handler,
      ) async {
    if (options.extra[ignoreRequestParam] == true){
      Logger.info(
          '---api-request--->url--> ${options.baseUrl}${options.path}, no params log because of config');
    }else{
      Logger.info(
          '---api-request--->url--> ${options.baseUrl}${options.path}, queryParameters: ${options.queryParameters}, header: ${options.headers}');
    }
    super.onRequest(options, handler);
  }

  @override
  onResponse(
      Response response,
      ResponseInterceptorHandler handler,
      ) {
    var options = response.requestOptions;

    if (options.extra[ignoreResponseLog] == true){
      Logger.info('---api-response---> url-->${options.baseUrl}${options.path}, no response log because of config');
    }else{
      Logger.info('---api-response---> url-->${options.baseUrl}${options.path}, resp----->${response.data!=null?jsonEncode(response.data):""}');
    }

    // handler.next(response);
    super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    var options=err.requestOptions;
    Logger.error("----api-erro---> url --- > ${options.baseUrl}${options.path} 请求失败: ${err.message}");
    super.onError(err, handler);
  }
}
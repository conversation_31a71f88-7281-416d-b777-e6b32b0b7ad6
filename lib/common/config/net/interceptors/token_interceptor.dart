import 'package:dio/dio.dart';

import '../../../../resource/shared_preferences_keys.dart';
import '../../../../utils/ui_util.dart';
import '../../build_config.dart';
import '../../router_manager.dart';
import '../../storage_manager.dart';
import '../business_code.dart';
import '../http_code.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/11/12
///   desc   : token拦截器
///   version: 1.0
class TokenInterceptor extends InterceptorsWrapper {
  ///是否需要登录,默认为需要登录
  static const String withoutLogin = "without-login";

  String? _token;

  @override
  onRequest(RequestOptions options, handler) {
    //授权码
    getAuthorization();
    addCommonParams(options.queryParameters);
    if (_token != null) {
      options.queryParameters['token']=_token;
      options.headers['token']=_token;
    } else {
      if (options.extra[withoutLogin] != true) {
        //需要登录
        toPage(RouteName.login);
        return handler.reject(
            DioError(
                requestOptions: options,
                type: DioErrorType.unknown,
                response: Response(
                    requestOptions: options, statusCode: HttpCode.noLogin)),
            true);
      }
    }
    return super.onRequest(options, handler);
  }

  void addCommonParams(Map<String, dynamic> queryParams){
      queryParams['channelCode']=BuildConfig.channelName;
      queryParams['deviceType']="android";
      queryParams['packet']=BuildConfig.packageName;
      queryParams['version']=BuildConfig.buildNumber;

  }

  @override
  onResponse(Response response, handler) async {
    if(response.data!=null && response.data is Map){
      if(response.data["code"] == BusinessCode.needLogin){
        if(!isCurrent(RouteName.login)){
          toPage(RouteName.login);
        }
      }
    }
    return super.onResponse(response, handler);
  }

  ///获取授权token
  getAuthorization() {
    String? token = StorageManager.sharedPreferences.getString(SharedPreferencesKeys.token);
    _token = token;
    return token;
  }
}

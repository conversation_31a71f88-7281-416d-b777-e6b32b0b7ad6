import 'package:dio/dio.dart';

import '../../../../log/log.dart';
import '../../../../utils/ui_util.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/11/19
///   desc   : loading拦截器
///   version: 1.0
class LoadingInterceptor extends InterceptorsWrapper {
  static const String autoLoading = "auto-loading";

  @override
  onRequest(RequestOptions options, handler) async {
    if (options.extra[autoLoading] == true) {
      showLoading();
    }
    super.onRequest(options, handler);
  }

  @override
  onResponse(Response response, handler) async {
    if (response.requestOptions.extra[autoLoading] == true) {
      dismissLoading();
    }
    return super.onResponse(response, handler);
  }

  @override
  onError(DioError err, handler) async {
    if (err.requestOptions.extra[autoLoading] != false) {
      dismissLoading();
    }
    return super.onError(err, handler);
  }
}

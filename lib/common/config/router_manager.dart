import 'dart:convert';

import 'package:easy_white_board/page/drawing_board/drawing_board_page.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

import '../../log/log.dart';
import '../../page/home/<USER>';
import '../../page/webview/webview_page.dart';
import '../../resource/shared_preferences_keys.dart';
import 'app_config.dart';
import 'application.dart';
import '../../api/api.dart';
import 'storage_manager.dart';

List<String> needLoginPage = [
];

class RouterManager {
  static late FluroRouter router;
  static String currentRouteName = "";
  static String preRouteName = "";
}

class RouteObserverHelper extends RouteObserver<PageRoute<dynamic>> {
  
  String getRouteName(String path){
    if(path.contains("?")){
      return path.split("?")[0];
    }
    return path;
  }

  void _sendScreenView(
      PageRoute<dynamic> route, PageRoute<dynamic> previousRoute) {
    RouterManager.currentRouteName = getRouteName(route.settings.name ?? "");
    RouterManager.preRouteName = getRouteName(previousRoute.settings.name??"");
    if(RouterManager.preRouteName==RouteName.drawingBoardPage){
      _supportOnlyVerticalScreen();
    }else{
      if(RouterManager.currentRouteName==RouteName.drawingBoardPage){
        _supportVerticalAndHorizontalScreen();
      }
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute && previousRoute is PageRoute) {
      _sendScreenView(route, previousRoute);

    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute is PageRoute && oldRoute is PageRoute) {
      _sendScreenView(newRoute, oldRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      _sendScreenView(previousRoute, route);
    }
  }


  void _supportVerticalAndHorizontalScreen(){
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight
    ]);
  }

  void _supportOnlyVerticalScreen(){
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}

const TransitionType globalTransitionType = TransitionType.cupertino;

back() {
  RouterManager.router.pop(MApplication.getContext());
}

popUntil(String roteName) {
  var navigator = Navigator.of(MApplication.getContext());
  navigator.popUntil(ModalRoute.withName(roteName));
}

bool isCurrent(String routeName) {
  return RouterManager.currentRouteName==routeName;
}

void toWebViewPage(String title,String url){
  RouterManager.router.navigateTo(
    MApplication.getContext(),
    '${RouteName.webViewPage}?title=${Uri.encodeComponent(title)}&url=${Uri.encodeComponent(url)}',
    transition: globalTransitionType,
  );
}

void toServicePage() {
  toWebViewPage("", Api.service_agreement);
}

void toPrivatePage() {
  toWebViewPage("", Api.privateAgreementUrl);
}

Future toPage(
  String routeName, {
  bool replace = false,
  bool clearStack = false,
  TransitionType transition = globalTransitionType,
  Duration transitionDuration = const Duration(milliseconds: 250),
  RouteTransitionsBuilder? transitionBuilder,
  Map<String, String>? params,
}) async {
  var context = MApplication.getContext();
  if (isCurrent(routeName)) {
    return;
  }
  if (needLoginPage.contains(routeName)) {
    if (AppConfig.loginEnabled) {
      String? token = StorageManager.sharedPreferences
          .getString(SharedPreferencesKeys.token);
      if (token == null) {
        return RouterManager.router.navigateTo(context, RouteName.login,
            transition: transition,
            replace: replace,
            clearStack: clearStack,
            transitionDuration: transitionDuration,
            transitionBuilder: transitionBuilder);
      }
    }
  }
  if (params != null && params.isNotEmpty) {
    String paramStr = "";
    params.forEach((key, value) {
      paramStr = '$paramStr$key=${Uri.encodeComponent(value)}&';
    });
    routeName = '$routeName?${paramStr.substring(0, paramStr.length - 1)}';
  }
  return RouterManager.router.navigateTo(context, routeName,
      transition: transition,
      replace: replace,
      clearStack: clearStack,
      transitionDuration: transitionDuration,
      transitionBuilder: transitionBuilder);
}

backUtil(String roteName) {
  var navigator = Navigator.of(MApplication.getContext());
  navigator.popUntil(ModalRoute.withName(roteName));
}

class RouteName {
  static const String webViewPage = 'web-view-page';
  static const String home = 'home';
  static const String login = 'login';
  static const String chargePage = 'chargePage';
  static const String drawingBoardPage = 'drawingBoardPage';

}

class Routes {
  static void configureRoutes(FluroRouter router) {
    router
      ..define(RouteName.webViewPage, handler: webViewPageHandler)
      ..define(RouteName.home, handler: homePageHandler)
      ..define(RouteName.drawingBoardPage, handler: drawingBoardPageHandler)
        ;

  }
}




var drawingBoardPageHandler = Handler(
  handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
    String? idStr=params['id']?.first;
    return   DrawingBoardPage(id: idStr==null?null:int.parse(idStr),);
  },
);
var homePageHandler = Handler(
  handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
    return const HomePage();
  },
);

var webViewPageHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
  String title = params['title']!.first;
  String url = params['url']!.first;
  String? isLocalHtml = params['isLocal']?.first;
  return WebViewPage(
    url,
    title,
    isLocal: isLocalHtml,
  );
});

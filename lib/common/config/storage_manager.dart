
import 'package:objectbox/objectbox.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

import '../../log/log.dart';
import '../../objectbox.g.dart';


class StorageManager {
  /// app全局配置 eg:theme
  static late SharedPreferences sharedPreferences;

  static ObjectBox? _objectbox;


  /// 必备数据的初始化操作
  ///
  /// 由于是同步操作会导致阻塞,所以应尽量减少存储容量
  static Future<SharedPreferences>  initSp() async {
    // async 异步操作
    // sync 同步操作
    sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences;
  }

  static Future<ObjectBox> initDb() async {
    _objectbox ??= await ObjectBox.create();
    return _objectbox!;
  }

  static ObjectBox get objectbox => _objectbox!;
}


class ObjectBox{
  late final Store store;
  static late final Admin _admin;

  ObjectBox._create(this.store){

  }

  static Future<ObjectBox> create() async {
    final docDir = await getApplicationDocumentsDirectory();
    var directory=p.join(docDir.path,"obx_db");
    var store;
    if(Store.isOpen(directory)){
      // applicable when store is from other isolate
      store= Store.attach(getObjectBoxModel(), directory);
    }else{
      store= await openStore(directory: directory);
    }

    Logger.info("=============== Admin.isAvailable():${Admin.isAvailable()}");
    if (Admin.isAvailable()) {
      // Keep a reference until no longer needed or manually closed.
      _admin = Admin(store);
    }
    return ObjectBox._create(store);
  }
}

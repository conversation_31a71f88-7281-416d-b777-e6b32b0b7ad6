



import '../../../api/api.dart';
import 'dart_define.dart';

class EnvConfig{
  final String env;
  final String domain;
  final String wsApiUrl;

  const EnvConfig({
    required this.env,
    required this.domain,
    required this.wsApiUrl,
  });
}

// 获取的配置信息
class Env {
  // 开发环境
  static const EnvConfig _devConfig = EnvConfig(
    env: EnvName.dev,
    // domain: "http://naganazhenre.tpddns.cn:5374",
    domain: Api.TEST_URL,
    wsApiUrl: Api.WS_API_URL,
  );

  // 测试环境
  static const EnvConfig _testConfig = EnvConfig(
    env: EnvName.test,
    // domain: "http://150.158.90.55:5281",
    domain:  Api.TEST_URL,
    wsApiUrl: Api.WS_API_URL,
  );

  // 发布环境
  static const EnvConfig _releaseConfig = EnvConfig(
    env: EnvName.release,
    domain:  Api.BASE_URL,
    wsApiUrl: Api.WS_API_URL,
  );

  static EnvConfig get envConfig => _getEnvConfig();

// 根据不同环境返回对应的环境配置
  static EnvConfig _getEnvConfig() {
    switch (DartDefine.appEnv) {
      case EnvName.dev:
        return _devConfig;
      case EnvName.release:
        return _releaseConfig;
      case EnvName.test:
        return _testConfig;
      default:
        return _devConfig;
    }
  }
}

// 声明的环境
abstract class EnvName {
  // 环境value
  static const String dev = "dev";
  static const String release = "release";
  static const String test = "test";
}
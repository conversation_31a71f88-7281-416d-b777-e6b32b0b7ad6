

import 'package:flutter/cupertino.dart';

class SimpleButton extends StatelessWidget{
  final String title;
  final Color? color;
  final Gradient? gradient;
  final Color textColor;
  final VoidCallback onTap;
  final double? radius;
  final double? width;
  final double? height;
  final  EdgeInsetsGeometry? margin;

  const SimpleButton({super.key,required this.title,  this.color,required this.textColor,required this.onTap,this.radius,this.width,this.height,this.gradient,this.margin}) ;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin,
        width: width,
        height: height??50,
        decoration: BoxDecoration(
          color: color,
          gradient: gradient,
          borderRadius: BorderRadius.circular(radius??25),
        ),
        child: Center(
          child: Text(title,style: TextStyle(color: textColor,fontSize: 16),),
        ),
      ),
    );
  }
}
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';




class BaseScaffold extends Scaffold {
  const BaseScaffold({
    super.key,
    super.appBar,
    super.body,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.floatingActionButtonAnimator,
    super.persistentFooterButtons,
    super.drawer,
    super.onDrawerChanged,
    super.endDrawer,
    super.onEndDrawerChanged,
    super.bottomNavigationBar,
    super.bottomSheet,
    super.backgroundColor= const  Color(0xffF6F6F6),
    super.resizeToAvoidBottomInset,
    super.primary = true,
    super.drawerDragStartBehavior = DragStartBehavior.start,
    super.extendBody = false,
    super.extendBodyBehindAppBar = false,
    super.drawerScrimColor,
    super.drawerEdgeDragWidth,
    super.drawerEnableOpenDragGesture = true,
    super.endDrawerEnableOpenDragGesture = true,
    super.restorationId,
  }) : super();


  const BaseScaffold.normalPage({
    Key? key, Widget? body,PreferredSizeWidget? appBar,Color? backgroundColor
  }):this(
    key: key,backgroundColor: backgroundColor ,body: body,appBar: appBar,
  );
}


import 'package:flutter/material.dart';

///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/08/09
///   desc   : BaseAppBar
///   version: 1.0
class BaseAppBar extends AppBar {
  BaseAppBar({
    super.key,
    bool noLeading = false,
    String titleText = "",
    Widget? leading,
    super.automaticallyImplyLeading,
    title,
    super.actions,
    super.flexibleSpace,
    super.bottom,
    super.elevation,
    super.scrolledUnderElevation,
    super.shadowColor=Colors.transparent,
    super.surfaceTintColor,
    super.shape,
    super.backgroundColor,
    super.foregroundColor,
    super.iconTheme,
    super.actionsIconTheme,
    super.primary = true,
    //title居中
    super.centerTitle = true,
    super.excludeHeaderSemantics = false,
    super.titleSpacing,
    super.toolbarOpacity = 1.0,
    super.bottomOpacity = 1.0,
    super.toolbarHeight,
    double leadingWidth = 40,
    super.toolbarTextStyle,
    super.titleTextStyle,
  }) : super(
    leadingWidth: noLeading ? 0 : leadingWidth,

    /// 默认返回的leading
    leading: leading ??
        Builder(builder: (context) {
          return !noLeading ? Container(
            padding: const EdgeInsets.only(left: 10),
            child:  ActionWidget(icon: Icons.arrow_back_ios,onTap: () async{
                Navigator.maybePop(context);
            },),
          ) : const Text('');
        }),
    title: title ??
        Builder(builder: (context) {
          return
            Text(
              titleText,
              maxLines: 1,
              // style: Theme.of(context).textTheme.titleLarge,
              overflow: TextOverflow.ellipsis,
            );
        }
        ),
  );


  BaseAppBar.normalAppBar({Key? key, required titleText,Widget? leading,List<Widget>? actions , Color? backgroundColor, Color? titleColor}):this(
    key:key,
    title: Text(titleText,style:   TextStyle(color:titleColor ,fontSize: 17 ),),
    backgroundColor: backgroundColor ,
    leading: leading??Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.only(left: 10),
        child:  ActionWidget(icon: Icons.arrow_back_ios,color: titleColor ,onTap: () async{
          Navigator.maybePop(context);
        },),
      );
    }),
    actions: actions,
  );


}


class ActionWidget extends StatelessWidget{

  final  IconData icon;
  final Color? color;

  final GestureTapCallback? onTap;

  const ActionWidget({super.key, required this.icon,this.onTap,this.color});


  @override
  Widget build(BuildContext context) {
    return   InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(left: 9,right:9),
        child: Icon(
          icon,
          size: 17,
          color: color??const Color(0xff0D1017),
          // color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }

}

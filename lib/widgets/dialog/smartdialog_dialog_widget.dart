import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/ui_util.dart';

class CustomAlertDialog extends StatelessWidget{

  final String title;
  final String description;
  GestureTapCallback? cancel;
  final GestureTapCallback confirm;
  final String? confirmText;
  String? cancelText;
  TextStyle? confirmStyle;
  TextStyle? cancelStyle;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final Color? bg;


  CustomAlertDialog({super.key,required this.title,required this.description,required this.confirm,this.cancel,this.cancelText,this.cancelStyle,this.confirmStyle,this.confirmText,this.descriptionStyle,this.titleStyle,this.bg }){
    cancel ??= (){
      dismissDialog();
    };
  }

  CustomAlertDialog.singleAction({super.key,required this.title,required this.description,required this.confirm,this.confirmStyle,this.confirmText,this.descriptionStyle,this.titleStyle,this.bg });


  @override
  Widget build(BuildContext context) {
    return _CustomDialog(
      title: Text(title,style: titleStyle??const TextStyle(fontSize: 17,fontWeight: FontWeight.w600,color: Colors.black),),
      confirm: confirm,
      confirmStyle: confirmStyle,
      confirmText: confirmText,
      cancel: cancel,
      cancelStyle: confirmStyle,
      cancelText: cancelText,
      bg: bg,child: Padding(
      padding: const EdgeInsets.only(top: 5.0),
      child: Text(description,style: descriptionStyle??const TextStyle(fontSize: 13,color: Colors.black)),
    ),
    );
  }


}

typedef EditConfirmCallBack=Function(String text);

class CustomAlertEditDialog extends StatelessWidget{

  final String title;

  final String? defaultValue;

  final Color? bg;

  final TextStyle? titleStyle;

  final EditConfirmCallBack callBack;

  final TextEditingController _controller=TextEditingController();

  final String? confirmText;
  int? maxCount;
  String? cancelText;
  TextStyle? confirmStyle;
  TextStyle? cancelStyle;
  final TextStyle? descriptionStyle;

  CustomAlertEditDialog({super.key,required this.title,this.bg,this.titleStyle,required this.callBack,this.confirmText,this.confirmStyle, this.cancelStyle,this.cancelText,this.descriptionStyle,this.defaultValue,this.maxCount});

  @override
  Widget build(BuildContext context) {
    _controller.text=defaultValue??"";
    return _CustomDialog(
      title: Text(title,style: titleStyle??const TextStyle(fontSize: 17,fontWeight: FontWeight.w600,color: Colors.black),),
      confirm: (){
        callBack(_controller.text);
      },
      confirmStyle: confirmStyle,
      confirmText: confirmText,
      cancel: () {
        dismissDialog();
      },
      cancelStyle: cancelStyle,
      cancelText: cancelText,
      bg: bg,child: Padding(
      padding: const EdgeInsets.only(top: 5.0),
      child: TextField(
        maxLength: maxCount,
        controller: _controller,
      ),
    ),
    );

  }

}

class _CustomDialog extends StatelessWidget{

  Widget? title;

  Widget? child;

  GestureTapCallback? cancel;
  final GestureTapCallback confirm;
  final String? confirmText;
  String? cancelText;
  TextStyle? confirmStyle;
  TextStyle? cancelStyle;
  final Color? bg;

  _CustomDialog({required this.title,  this.child,required this.confirm,this.cancel,this.cancelText,this.cancelStyle,this.confirmStyle,this.confirmText, this.bg });


  @override
  Widget build(BuildContext context) {
    List<Widget> children=[];
    if(title!=null){
      children.add(title!);
    }
    if(child!=null){
      children.add(child!);
    }
    List<Widget> actions=[];
    if(cancel!=null){
      actions.add(
          _actionButton(cancelText??"取消", cancelStyle??const TextStyle(fontSize: 17,color: Color(0xff007aff)), cancel,const Border(
            top: BorderSide(color: Color(0xfff6f6f6)),
            right: BorderSide(color: Color(0xfff6f6f6)),
          ))
      );
    }
    actions.add(_actionButton(confirmText??"确定", confirmStyle??const TextStyle(fontSize: 17,fontWeight: FontWeight.w600,color:Color(0xff007aff) ), confirm,  const Border(
      top: BorderSide(color: Color(0xfff6f6f6)),
    )));
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 43.w),
      decoration: BoxDecoration(
        borderRadius:BorderRadius.circular(20),
        color: bg??Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding:   EdgeInsets.symmetric(horizontal: 16.w,vertical: 19.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: children,
            ),
          ),
          Flex(

            direction: Axis.horizontal,
            children: actions,
          )
        ],
      ),
    );
  }



  Widget _actionButton(String text,TextStyle style,GestureTapCallback? onTap,Border border){
    return Expanded(
        flex: 1,
        child: InkWell(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              border: border,
            ),
            padding:   EdgeInsets.symmetric(vertical: 11.w),
            alignment: Alignment.center,
            child: Text(text,style: style,),
          ),
        ));
  }

}
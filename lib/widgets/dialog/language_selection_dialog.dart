import 'package:easy_white_board/common/localizations/global_localizations_delegate.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../common/extension/build_context_ext.dart';
import '../../provider/business/language_provider.dart';

class LanguageSelectionDialog extends ConsumerWidget {
  const LanguageSelectionDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(languageProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(20.w),
        constraints: BoxConstraints(
          maxHeight: 600.h,
          maxWidth: 400.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              context.string.selectLanguage,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20.h),

            // 当前语言显示
            if (currentLanguage != null)
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Text(
                      currentLanguage?.flag ?? "",
                      style: TextStyle(fontSize: 24.sp),
                    ),
                    SizedBox(width: 12.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.string.currentLanguage,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          currentLanguage?.nativeName ?? "",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

            SizedBox(height: 20.h),

            // 语言列表
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: SupportedLanguages.languages.length,
                itemBuilder: (context, index) {
                  final language = SupportedLanguages.languages[index];
                  String languageCode = language.languageCode;
                   bool isSelected = language == currentLanguage;
                  if (currentLanguage == null) {
                    isSelected = languageCode ==
                        GlobalLocalizationsDelegate.delegate
                            .getCurrentLanguageCode(context);
                  }

                  return Container(
                    margin: EdgeInsets.only(bottom: 8.h),
                    child: ListTile(
                      leading: Text(
                        language.flag,
                        style: TextStyle(fontSize: 24.sp),
                      ),
                      title: Text(
                        language.nativeName,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      subtitle: Text(
                        language.displayName,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle,
                              color: Colors.blue,
                              size: 24.w,
                            )
                          : null,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      tileColor: isSelected
                          ? Colors.blue.withValues(alpha: 0.1)
                          : null,
                      onTap: () async {
                        if (!isSelected) {
                          await ref
                              .read(languageProvider.notifier)
                              .updateLanguage(language);
                        }
                        if (context.mounted) {
                          Navigator.of(context).pop();
                        }
                      },
                    ),
                  );
                },
              ),
            ),

            SizedBox(height: 20.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  context.string.cancel,
                  style: TextStyle(fontSize: 16.sp),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

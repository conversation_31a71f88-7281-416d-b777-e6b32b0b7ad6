import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/ui_util.dart';

class SimpleTwoChoiceDialog extends StatelessWidget {
  final String? imageIcon;
  final String? title;
  final Widget? description;
  final String? descriptionString;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final String? actionButton1Text;
  final String? actionButton2Text;
  final TextStyle? actionButton1Style;
  final TextStyle? actionButton2Style;
  final Decoration? actionButton1Decoration;
  final Decoration? actionButton2Decoration;
  final GestureTapCallback? onCloseTap;
  final GestureTapCallback? onActionButton1Tap;
  final GestureTapCallback? onActionButton2Tap;
  final Color? bg;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? imageWidth;

  const SimpleTwoChoiceDialog({
    super.key,
    this.imageIcon,
    this.imageWidth,
    this.title,
    this.description,
    this.descriptionString,
    this.titleStyle,
    this.descriptionStyle,
    this.actionButton1Text,
    this.actionButton2Text,
    this.onCloseTap,
    this.onActionButton1Tap,
    this.onActionButton2Tap,
    this.bg,
    this.padding,
    this.margin,
    this.actionButton1Style,
    this.actionButton2Style,
    this.actionButton1Decoration,
    this.actionButton2Decoration,
  });

  @override
  Widget build(BuildContext context) {
    return  TwoChoiceDialog(
      imageIcon: imageIcon!=null?Image.asset(imageIcon!,width: imageWidth?? 60.w,):null,
      title: title!=null?Text(title!,style: titleStyle??const TextStyle(color: Color(0xff0D1017
      ),fontSize: 16,fontWeight: FontWeight.bold),):null,
      description: description??(descriptionString!=null?Text(descriptionString!,style: descriptionStyle??const TextStyle(color: Color(0xff00A0FB),fontSize: 14),):null),
      actionButton1: actionButton1Text!=null?InkWell(
        onTap: onActionButton1Tap,
        child: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(vertical: 10.h),
            margin: EdgeInsets.symmetric(horizontal: 22.w),
            decoration: actionButton1Decoration??BoxDecoration(
              borderRadius: BorderRadius.circular(6.w),
              color: const Color(0xff2D81FF)
            ),
          child: Text(actionButton1Text!,style: actionButton1Style??const TextStyle(fontSize:14,color: Colors.white ),),
        ),
      ):null,
      actionButton2: actionButton2Text!=null?InkWell(
        onTap: onActionButton2Tap,
        child: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(vertical: 10.h),
            margin: EdgeInsets.symmetric(horizontal: 22.w),
            decoration: actionButton2Decoration??BoxDecoration(
              borderRadius: BorderRadius.circular(6.w),
              color: Colors.white,
              border: Border.all(color: const Color(0xff00A0FB))
            ),
            child: Text(actionButton2Text!,style: actionButton2Style??const TextStyle(fontSize:14,color: Color(0xff00A0FB) ),)
        ),
      ):null,
    );
  }
}

class TwoChoiceDialog extends StatelessWidget {
  final Widget? imageIcon;
  final Widget? title;
  final Widget? description;
  final Widget? actionButton1;
  final Widget? actionButton2;
  final double? boarderRadius;
  final Color? bg;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final GestureTapCallback? onCloseTap;

  const TwoChoiceDialog(
      {super.key,
      this.imageIcon,
      this.title,
      this.description,
      this.actionButton1,
      this.actionButton2,
      this.boarderRadius,
      this.bg,
      this.padding,
      this.margin,
      this.onCloseTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _bodyView(),
        InkWell(
          onTap: onCloseTap ?? ()=>dismissDialog(),
          child:
          Padding(
            padding: EdgeInsets.all(12.h),
            child: Icon(Icons.close,size: 18.w,),
          ),
        )
      ],
    );
  }

  Widget _bodyView() {
    return Container(
      margin: margin ?? EdgeInsets.symmetric(horizontal: 39.w),
      padding:
          padding ?? EdgeInsets.symmetric(horizontal: 26.w, vertical: 40.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(boarderRadius ?? 20.w),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Offstage(
            offstage: imageIcon == null,
            child: Padding(
              padding: EdgeInsets.only(bottom: 10.h),
              child: imageIcon,
            ),
          ),
          Offstage(
            offstage: title == null,
            child: Padding(
              padding: EdgeInsets.only(bottom: 10.h),
              child: title,
            ),
          ),
          Offstage(
            offstage: description == null,
            child: Padding(
              padding: EdgeInsets.only(bottom: 14.h),
              child: description,
            ),
          ),
          Offstage(
            offstage: actionButton1 == null,
            child: Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: actionButton1,
            ),
          ),
          Offstage(offstage: actionButton2 == null, child: actionButton2),
        ],
      ),
    );
  }
}

import 'dart:convert';

import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../common/config/router_manager.dart';
import '../../../model/db/drawing_data.dart';
import '../home_controller.dart';

class DrawingItemWidget extends StatefulWidget {
  final HomeController controller;
  final DrawingData data;

  const DrawingItemWidget(
      {super.key, required this.data, required this.controller});

  @override
  State<StatefulWidget> createState() {
    return _DrawingItemWidgetState();
  }
}

class _DrawingItemWidgetState extends State<DrawingItemWidget> {
  bool isEdit = false;

  @override
  Widget build(BuildContext context) {
    var create = DateTime.fromMillisecondsSinceEpoch(widget.data.createdTime);
    String createTime =
        "${create.year}-${create.month}-${create.day} ${create.hour}:${create.minute}:${create.second}";
    return InkWell(
      onLongPress: () {
        if (!isEdit) {
          isEdit = true;
          setState(() {});
        }
      },
      onTap: () {
        if (isEdit) {
          isEdit = false;
          setState(() {});
        } else {
          toDrawingBoardPage(id: widget.data.id!);
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                width: 160.w,
                height: 120.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.w),
                    image: widget.data.imageStr != null
                        ? DecorationImage(
                            image: MemoryImage(
                                base64Decode(widget.data.imageStr!)),
                            fit: BoxFit.fill)
                        : null),
              ),
              Positioned(
                bottom: 10.h,
                child: Offstage(
                  offstage: !isEdit,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          isEdit = false;
                          widget.controller
                              .onRenameClicked(context, widget.data);
                        },
                        child: Padding(
                          padding: EdgeInsets.only(left: 20.w),
                          child: const Icon(Icons.edit_note),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          isEdit = false;
                          widget.controller
                              .onDeleteClicked(context, widget.data.id!);
                        },
                        child: Padding(
                          padding: EdgeInsets.only(left: 10.w),
                          child: const Icon(Icons.delete_forever),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.0.h, left: 2.w, right: 2.w),
            child: Text(
              widget.data.name ?? context.string.untitled,
              style:
                  TextStyle(fontSize: 14.sp, overflow: TextOverflow.ellipsis),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Text(
              createTime,
              style: TextStyle(fontSize: 12.sp, color: Colors.black12),
            ),
          ),
        ],
      ),
    );
  }

  void toDrawingBoardPage({int? id}) {
    toPage(
      RouteName.drawingBoardPage,
      params: id != null ? {'id': id.toString()} : null,
      replace: true,
      clearStack: true,
      transition: TransitionType.custom,
      transitionBuilder: (
        BuildContext context,
        Animation<double> animation,
        Animation<double> secondaryAnimation,
        Widget child,
      ) =>
          Align(
        child: SizeTransition(
          sizeFactor: animation,
          child: child,
        ),
      ),
    );
  }
}

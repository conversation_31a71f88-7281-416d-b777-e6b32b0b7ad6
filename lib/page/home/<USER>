


import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/model/db/drawing_data.dart';
import 'package:easy_white_board/provider/business/drawing_data_provider.dart';
import 'package:easy_white_board/utils/ui_util.dart';
import 'package:easy_white_board/widgets/dialog/smartdialog_dialog_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../common/config/storage_manager.dart';
import '../../objectbox.g.dart';




class HomeController{


  final WidgetRef ref;

  late DrawingDataNotifier drawingDataNotifier;

  HomeController(this.ref){
    drawingDataNotifier=ref.read(drawingDataProvider.notifier);
  }


  Future onRenameClicked(BuildContext context,DrawingData data) async {
    return await showSmartDialog(CustomAlertEditDialog(title: context.string.renameDrawing,
        confirmText: context.string.confirm,
        cancelText: context.string.cancel,
        defaultValue: data.name,
        callBack: (String value){
        if(value.isNotEmpty){
          data.name=value;
          drawingDataNotifier.saveDrawingData(data);
          dismissDialog();
        }else{
          showToast(context.string.nameCouldNotEmpty);
        }
    }));
  }

  Future onDeleteClicked(BuildContext context,int id) async {
    return await showSmartDialog(CustomAlertDialog(title: context.string.confirmToDelete, description: "",
        confirmText: context.string.delete,
        cancelText: context.string.cancel,
        confirm: (){
          drawingDataNotifier.deleteData(id);
          dismissDialog();
    }));
  }

}
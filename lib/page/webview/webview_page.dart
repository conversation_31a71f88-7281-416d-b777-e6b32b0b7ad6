


import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';


class WebViewPage extends StatefulWidget {
  final String url;
  final String title;
  final String? isLocal;
  final bool clearCache;
  final Function(String)? onUrlChanged;

  const WebViewPage(this.url, this.title, {super.key, this.isLocal='false',this.clearCache=false,this.onUrlChanged});

  @override
  State createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {

  final GlobalKey webViewKey = GlobalKey();

  InAppWebViewController? webViewController;
  InAppWebViewSettings options = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      useHybridComposition: true,
    allowsInlineMediaPlayback: true,
  );

  late PullToRefreshController pullToRefreshController;

  @override
  void initState() {
    super.initState();
    pullToRefreshController = PullToRefreshController(
      options: PullToRefreshOptions(
        color: Colors.blue,
      ),
      onRefresh: () async {
        if (Platform.isAndroid) {
          webViewController?.reload();
        } else if (Platform.isIOS) {
          webViewController?.loadUrl(
              urlRequest: URLRequest(url: await webViewController?.getUrl()));
        }
      },
    );
  }


  @override
  Widget build(BuildContext context) {

      return Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
        ),
        body: InAppWebView(
          key: webViewKey,
          initialUrlRequest:
          URLRequest(url: WebUri.uri(Uri.parse(widget.url))),
          initialSettings: options,
          pullToRefreshController: pullToRefreshController,
          onWebViewCreated: (controller) {
            webViewController = controller;
          },
        ),
      );
  }

}

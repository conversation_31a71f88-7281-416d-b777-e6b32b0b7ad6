

import 'dart:ui';

import 'package:easy_white_board/log/log.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/paint_content.dart';
import 'package:flutter/material.dart';

import '../helper/safe_value_notifier.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';


/// 控件橡皮擦
/// 可以删除框住的所有控件
class ContentEraser extends PaintContent{

  /// 起始点
  Offset startPoint = Offset.zero;

  /// 结束点
  Offset endPoint = Offset.zero;

  ContentEraser();

  ContentEraser.data({
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
    required this.swipedIds,
  }) : super.paint(paint);

  factory ContentEraser.fromJson(Map<String, dynamic> data) {
    Logger.info("===== swipedContents:${data['swipedContents']}");
    return ContentEraser.data(
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
      swipedIds:(data['swipedContents'] as List<dynamic>).map((e) => e.toString()).toList(),
    );
  }


  List<String> swipedIds=[];

  void doSwipe(String id){
    swipedIds.add(id);
  }
  void doSwipes(List<String> ids){
    swipedIds.addAll(ids);
  }

  @override
  PaintContent copy()=>ContentEraser();

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    if(endPoint.dx==0 && endPoint.dy==0){
      return;
    }
    if(!deeper){
      paint.color=Colors.red;
      canvas.drawRect(Rect.fromPoints(startPoint, endPoint), paint.copyWith(color: Colors.black12));
      endDraw(endPoint, history, currentIndex);
    }

  }

  void calculateSwipeContent(List<PaintContent> history){
    List<String> savedIds=[];
    for (var element in history.reversed) {
      if(element is ContentEraser){
        savedIds.addAll(element.swipedIds);
      }
    }
    for(var element in history){
      String id=element.id;
      if(!savedIds.contains(id)){
        if(element.isIntersectingRectangle(Rect.fromPoints(startPoint, endPoint))){
          if(!swipedIds.contains(id)){
            doSwipe(id);
          }
        }
      }
    }
  }

  @override
  void startDraw(Offset startPoint){
    this.startPoint = startPoint;
  }

  @override
  void drawing(Offset nowPoint){
    endPoint = nowPoint;
  }

  void endDraw(Offset? endPoint, List<PaintContent> history, SafeValueNotifier<int> currentIndex) {
    if(endPoint?.dx.toString() == startPoint.dx.toString() || endPoint?.dy.toString() == startPoint.dy.toString()){
      return;
    }
    //  橡皮擦逻辑
    calculateSwipeContent(history);
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
      'swipedContents':swipedIds
    };
  }

 @override
  Future<bool> isValidContent() async{
     return swipedIds.isNotEmpty;
  }

}
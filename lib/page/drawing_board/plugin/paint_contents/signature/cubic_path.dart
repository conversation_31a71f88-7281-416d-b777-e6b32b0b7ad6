
import 'dart:ui';

import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/signature/utils.dart';
import 'dart:math' as math;

/// Extended [Offset] point with [timestamp].
class OffsetPoint extends Offset {
  /// Timestamp of this point. Used to determine velocity to other points.
  final int timestamp;

  /// 2D point in canvas space.
  /// [timestamp] of this [Offset]. Used to determine velocity to other points.
  const OffsetPoint({
    required double dx,
    required double dy,
    required this.timestamp,
  }) : super(dx, dy);

  factory OffsetPoint.from(Offset offset) => OffsetPoint(
    dx: offset.dx,
    dy: offset.dy,
    timestamp: DateTime.now().millisecondsSinceEpoch,
  );

  factory OffsetPoint.fromJson(Map<String, dynamic> map) => OffsetPoint(
    dx: map['x'],
    dy: map['y'],
    timestamp: map['t'],
  );

  Map<String, dynamic> toJson() => {
    'x': dx,
    'y': dy,
    't': timestamp,
  };


  /// Returns velocity between this and [other] - previous point.
  double velocityFrom(OffsetPoint other) => timestamp != other.timestamp
      ? distanceTo(other) / (timestamp - other.timestamp)
      : 0.0;

  @override
  OffsetPoint translate(double translateX, double translateY) {
    return OffsetPoint(
      dx: dx + translateX,
      dy: dy + translateY,
      timestamp: timestamp,
    );
  }

  @override
  OffsetPoint scale(double scaleX, double scaleY) {
    return OffsetPoint(
      dx: dx * scaleX,
      dy: dy * scaleY,
      timestamp: timestamp,
    );
  }

  @override
  bool operator ==(other) {
    return other is OffsetPoint &&
        other.dx == dx &&
        other.dy == dy &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => Object.hash(super.hashCode, timestamp);
}

/// Line between two points. Curve of this line is controlled with other two points.
/// Check https://cubic-bezier.com/ for more info about Bezier Curve.
class CubicLine extends Offset {
  /// Initial point of curve.
  final OffsetPoint start;

  /// Control of [start] point.
  final Offset cpStart;

  /// Control of [end] point
  final Offset cpEnd;

  /// End point of curve.
  final OffsetPoint end;

  late double _velocity;
  late double _distance;

  /// Cache of Up vector.
  Offset? _upStartVector;

  /// Up vector of [start] point.
  Offset get upStartVector =>
      _upStartVector ??
          (_upStartVector = start.directionTo(point(0.001)).rotate(-math.pi * 0.5));

  /// Cache of Up vector.
  Offset? _upEndVector;

  /// Up vector of [end] point.
  Offset get upEndVector =>
      _upEndVector ??
          (_upEndVector = end.directionTo(point(0.999)).rotate(math.pi * 0.5));

  /// Down vector.
  Offset get _downStartVector => upStartVector.rotate(math.pi);

  /// Down vector.
  Offset get _downEndVector => upEndVector.rotate(math.pi);

  /// Start ratio size of line.
  double startSize;

  /// End ratio size of line.
  double endSize;

  /// Checks if point is dot.
  /// Returns 'true' if [start] and [end] is same -> [velocity] is zero.
  bool get isDot => _velocity == 0.0;

  /// Based on Bezier Cubic curve.
  /// [start] point of curve.
  /// [end] point of curve.
  /// [cpStart] - control point of [start] vector.
  /// [cpEnd] - control point of [end] vector.
  /// [startSize] - size ratio at begin of curve.
  /// [endSize] - size ratio at end of curve.
  /// [upStartVector] - pre-calculated Up vector fo start point.
  /// [upEndVector] - pre-calculated Up vector of end point.
  CubicLine({
    required this.start,
    required this.cpStart,
    required this.cpEnd,
    required this.end,
    Offset? upStartVector,
    Offset? upEndVector,
    this.startSize = 0.0,
    this.endSize = 0.0,
  }) : super(start.dx, start.dy) {
    _upStartVector = upStartVector;
    _upEndVector = upEndVector;
    _velocity = end.velocityFrom(start);
    _distance = start.distanceTo(end);
  }

  @override
  CubicLine scale(double scaleX, double scaleY) => CubicLine(
    start: start.scale(scaleX, scaleY),
    cpStart: cpStart.scale(scaleX, scaleY),
    cpEnd: cpEnd.scale(scaleX, scaleY),
    end: end.scale(scaleX, scaleY),
    upStartVector: _upStartVector,
    upEndVector: _upEndVector,
    startSize: startSize * (scaleX + scaleY) * 0.5,
    endSize: endSize * (scaleX + scaleY) * 0.5,
  );

  @override
  CubicLine translate(double translateX, double translateY) => CubicLine(
    start: start.translate(translateX, translateY),
    cpStart: cpStart.translate(translateX, translateY),
    cpEnd: cpEnd.translate(translateX, translateY),
    end: end.translate(translateX, translateY),
    upStartVector: _upStartVector,
    upEndVector: _upEndVector,
    startSize: startSize,
    endSize: endSize,
  );

  /// Calculates length of Cubic curve with given [accuracy].
  /// 0 - fastest, raw accuracy.
  /// 1 - slowest, most accurate.
  /// Returns length of curve.
  double length({double accuracy = 0.1}) {
    final steps = (accuracy * 100).toInt();

    if (steps <= 1) {
      return _distance;
    }

    double length = 0.0;

    Offset prevPoint = start;
    for (int i = 1; i < steps; i++) {
      final t = i / steps;

      final next = point(t);

      length += prevPoint.distanceTo(next);
      prevPoint = next;
    }

    return length;
  }

  /// Calculates point on curve at given [t].
  /// [t] - 0 to 1.
  /// Returns location on Curve at [t].
  Offset point(double t) {
    final rt = 1.0 - t;
    return (start * rt * rt * rt) +
        (cpStart * 3.0 * rt * rt * t) +
        (cpEnd * 3.0 * rt * t * t) +
        (end * t * t * t);
  }

  /// Velocity along this line.
  double velocity({double accuracy = 0.0}) => start.timestamp != end.timestamp
      ? length(accuracy: accuracy) / (end.timestamp - start.timestamp)
      : 0.0;

  /// Combines line velocity with [inVelocity] based on [velocityRatio].
  double combineVelocity(double inVelocity,
      {double velocityRatio = 0.65, double maxFallOff = 1.0}) {
    final value =
        (_velocity * velocityRatio) + (inVelocity * (1.0 - velocityRatio));

    maxFallOff *= _distance / 10.0;

    final dif = value - inVelocity;
    if (dif.abs() > maxFallOff) {
      if (dif > 0.0) {
        return inVelocity + maxFallOff;
      } else {
        return inVelocity - maxFallOff;
      }
    }

    return value;
  }

  /// Converts this line to Cubic [Path].
  Path toPath() => Path()
    ..moveTo(dx, dy)
    ..cubicTo(cpStart.dx, cpStart.dy, cpEnd.dx, cpEnd.dy, end.dx, end.dy);

  /// Converts this line to [CubicArc].
  List<CubicArc> toArc(double size, double deltaSize,
      {double precision = 0.5}) {
    final list = <CubicArc>[];

    final steps = (_distance * precision).floor().clamp(1, 30);

    Offset start = this.start;
    for (int i = 0; i < steps; i++) {
      final t = (i + 1) / steps;
      final loc = point(t);
      final width = size + deltaSize * t;

      list.add(CubicArc(
        start: start,
        location: loc,
        size: width,
      ));

      start = loc;
    }

    return list;
  }

  /// Converts this line to closed [Path].
  Path toShape(double size, double maxSize) {
    final startArm = (size + (maxSize - size) * startSize) * 0.5;
    final endArm = (size + (maxSize - size) * endSize) * 0.5;

    final sDirUp = upStartVector;
    final eDirUp = upEndVector;

    final d1 = sDirUp * startArm;
    final d2 = eDirUp * endArm;
    final d3 = eDirUp.rotate(math.pi) * endArm;
    final d4 = sDirUp.rotate(math.pi) * startArm;

    return Path()
      ..start(start + d1)
      ..cubic(cpStart + d1, cpEnd + d2, end + d2)
      ..line(end + d3)
      ..cubic(cpEnd + d3, cpStart + d4, start + d4)
      ..close();
  }

  /// Returns Up offset of start point.
  Offset cpsUp(double size, double maxSize) =>
      upStartVector * startRadius(size, maxSize);

  /// Returns Up offset of end point.
  Offset cpeUp(double size, double maxSize) =>
      upEndVector * endRadius(size, maxSize);

  /// Returns Down offset of start point.
  Offset cpsDown(double size, double maxSize) =>
      _downStartVector * startRadius(size, maxSize);

  /// Returns Down offset of end point.
  Offset cpeDown(double size, double maxSize) =>
      _downEndVector * endRadius(size, maxSize);

  /// Returns radius of start point.
  double startRadius(double size, double maxSize) =>
      _lerpRadius(size, maxSize, startSize);

  /// Returns radius of end point.
  double endRadius(double size, double maxSize) =>
      _lerpRadius(size, maxSize, endSize);

  /// Linear interpolation of size.
  /// Returns radius of interpolated size.
  double _lerpRadius(double size, double maxSize, double t) =>
      (size + (maxSize - size) * t) * 0.5;

  /// Calculates [current] point based on [previous] and [next] control points.
  static Offset softCP(OffsetPoint current,
      {OffsetPoint? previous,
        OffsetPoint? next,
        bool reverse = false,
        double smoothing = 0.65}) {
    assert(smoothing >= 0.0 && smoothing <= 1.0);

    previous ??= current;
    next ??= current;

    final sharpness = 1.0 - smoothing;

    final dist1 = previous.distanceTo(current);
    final dist2 = current.distanceTo(next);
    final dist = dist1 + dist2;
    final dir1 = current.directionTo(next);
    final dir2 = current.directionTo(previous);
    final dir3 =
    reverse ? next.directionTo(previous) : previous.directionTo(next);

    final velocity =
    (dist * 0.3 / (next.timestamp - previous.timestamp)).clamp(0.5, 3.0);
    final ratio = (dist * velocity * smoothing)
        .clamp(0.0, (reverse ? dist2 : dist1) * 0.5);

    final dir =
        ((reverse ? dir2 : dir1) * sharpness) + (dir3 * smoothing) * ratio;
    final x = current.dx + dir.dx;
    final y = current.dy + dir.dy;

    return Offset(x, y);
  }

  @override
  bool operator ==(Object other) =>
      other is CubicLine &&
          start == other.start &&
          cpStart == other.cpStart &&
          cpEnd == other.cpEnd &&
          end == other.end &&
          startSize == other.startSize &&
          endSize == other.endSize;

  @override
  int get hashCode =>
      super.hashCode ^
      start.hashCode ^
      cpStart.hashCode ^
      cpEnd.hashCode ^
      end.hashCode ^
      startSize.hashCode ^
      endSize.hashCode;
}

/// Arc between two points.
class CubicArc extends Offset {
  static const _pi2 = math.pi * 2.0;

  /// End location of arc.
  final Offset location;

  /// Line size.
  final double size;

  /// Arc path.
  Path get path => Path()
    ..moveTo(dx, dy)
    ..arcToPoint(location, rotation: _pi2);

  /// Rectangle of start and end point.
  Rect get rect => Rect.fromPoints(this, location);

  /// Arc line.
  /// [start] point of arc.
  /// [location] end point of arc.
  /// [size] ratio of arc. typically 0 - 1.
  CubicArc({
    required Offset start,
    required this.location,
    this.size = 1.0,
  }) : super(start.dx, start.dy);

  @override
  Offset translate(double translateX, double translateY) => CubicArc(
    start: Offset(dx + translateX, dy + translateY),
    location: location.translate(translateX, translateY),
    size: size,
  );

  @override
  Offset scale(double scaleX, double scaleY) => CubicArc(
    start: Offset(dx * scaleX, dy * scaleY),
    location: location.scale(scaleX, scaleY),
    size: size * (scaleX + scaleY) * 0.5,
  );
}

/// Combines sequence of points into one Line.
class CubicPath {
  /// Raw data.
  final _points = <OffsetPoint>[];

  /// [CubicLine] representation of path.
  final _lines = <CubicLine>[];

  /// [CubicArc] representation of path.
  final _arcs = <CubicArc>[];

  /// Distance between two control points.
  final double threshold;

  /// Ratio of line smoothing.
  /// Don't have impact to performance. Values between 0 - 1.
  /// [0] - no smoothing, no flattening.
  /// [1] - best smoothing, but flattened.
  /// Best results are between: 0.5 - 0.85.
  final double smoothRatio;

  /// Returns raw data of path.
  List<OffsetPoint> get points => _points;

  /// Returns [CubicLine] representation of path.
  List<CubicLine> get lines => _lines;

  /// Returns [CubicArc] representation of path.
  List<CubicArc> get arcs => _arcs;

  /// First point of path.
  Offset? get _origin => _points.isNotEmpty ? _points[0] : null;

  /// Last point of path.
  OffsetPoint? get _lastPoint =>
      _points.isNotEmpty ? _points[_points.length - 1] : null;

  /// Checks if path is valid.
  bool get isFilled => _lines.isNotEmpty;

  /// Checks if this Line is just dot.
  bool get isDot => lines.length == 1 && lines[0].isDot;

  /// Unfinished path.
  // Path? _temp;

  /// Returns currently unfinished part of path.
  // Path? get tempPath => _temp;

  /// Maximum possible velocity.
  double maxVelocity = 1.0;


  /// Actual average velocity.
  double currentVelocity = 0.0;



  /// Actual size based on velocity.
  double currentSize = 0.0;



  /// Line builder.
  /// [threshold] - Distance between two control points.
  /// [smoothRatio] - Ratio of line smoothing.
  CubicPath({
    this.threshold = 3.0,
    this.smoothRatio = 0.65,
  });

  /// Adds line to path.
  void _addLine(CubicLine line) {
    if (_lines.isEmpty) {
      if (currentVelocity == 0.0) {
        currentVelocity = line._velocity;
      }

      if (currentSize == 0.0) {
        currentSize = _lineSize(currentVelocity, maxVelocity);
      }
    } else {
      line._upStartVector = _lines.last.upEndVector;
    }

    _lines.add(line);

    final combinedVelocity =
    line.combineVelocity(currentVelocity, maxFallOff: 0.125);
    final double endSize = _lineSize(combinedVelocity, maxVelocity);

    if (combinedVelocity > maxVelocity) {
      maxVelocity = combinedVelocity;
    }

    line.startSize = currentSize;
    line.endSize = endSize;

    _arcs.addAll(line.toArc(currentSize, endSize - currentSize));

    currentSize = endSize;
    currentVelocity = combinedVelocity;
  }

  /// Adds dot to path.
  void _addDot(CubicLine line) {
    final size = 0.25 + _lineSize(currentVelocity, maxVelocity) * 0.5;
    line.startSize = size;

    _lines.add(line);
    _arcs.addAll(line.toArc(size, 0.0));
  }

  /// Calculates line size based on [velocity].
  double _lineSize(double velocity, double max) {
    velocity /= max;

    return 1.0 - velocity.clamp(0.0, 1.0);
  }

  /// Starts path at given [point].
  /// Must be called as first, before [begin], [end].
  void begin(Offset point, {double velocity = 0.0}) {
    _points.add(point is OffsetPoint ? point : OffsetPoint.from(point));
    currentVelocity = velocity;
  }

  /// Alters path with given [point].
  void add(Offset point) {
    assert(_origin != null);

    final nextPoint = point is OffsetPoint ? point : OffsetPoint.from(point);

    if (_lastPoint == null || _lastPoint!.distanceTo(nextPoint) < threshold) {
      return;
    }

    _points.add(nextPoint);
    int count = _points.length;

    if (count < 3) {
      return;
    }

    int i = count - 3;

    final prev = i > 0 ? _points[i - 1] : _points[i];
    final start = _points[i];
    final end = _points[i + 1];
    final next = _points[i + 2];

    final cpStart = CubicLine.softCP(
      start,
      previous: prev,
      next: end,
      smoothing: smoothRatio,
    );

    final cpEnd = CubicLine.softCP(
      end,
      previous: start,
      next: next,
      smoothing: smoothRatio,
      reverse: true,
    );

    final line = CubicLine(
      start: start,
      cpStart: cpStart,
      cpEnd: cpEnd,
      end: end,
    );

    _addLine(line);
  }

  /// Ends path at given [point].
  bool end({Offset? point}) {
    if (point != null) {
      add(point);
    }

    if (_points.isEmpty) {
      return false;
    }

    if (_points.length < 3) {
      if (_points.length == 1 || _points[0].distanceTo(points[1]) == 0.0) {
        _addDot(CubicLine(
          start: _points[0],
          cpStart: _points[0],
          cpEnd: _points[0],
          end: _points[0],
        ));
      } else {
        _addLine(CubicLine(
          start: _points[0],
          cpStart: _points[0],
          cpEnd: _points[1],
          end: _points[1],
        ));
      }
    } else {
      final i = _points.length - 3;

      if (_points[i + 1].distanceTo(points[i + 2]) > 0.0) {
        _addLine(CubicLine(
          start: _points[i + 1],
          cpStart: _points[i + 1],
          cpEnd: _points[i + 2],
          end: _points[i + 2],
        ));
      }
    }

    return true;
  }

  /// Sets scale of whole line.
  void setScale(double ratio) {
    if (!isFilled) {
      return;
    }

    final arcData = PathUtil.scale<CubicArc>(_arcs, ratio);
    _arcs
      ..clear()
      ..addAll(arcData);

    final lineData = PathUtil.scale<CubicLine>(_lines, ratio);
    _lines
      ..clear()
      ..addAll(lineData);
  }

  /// Clears all path data-.
  void clear() {
    _points.clear();
    _lines.clear();
    _arcs.clear();
  }

  /// Currently checks only equality of [points].
  bool equals(CubicPath other) {
    if (points.length == other.points.length) {
      for (int i = 0; i < points.length; i++) {
        if (points[i] != other.points[i]) {
          return false;
        }
      }

      return true;
    }

    return false;
  }
}

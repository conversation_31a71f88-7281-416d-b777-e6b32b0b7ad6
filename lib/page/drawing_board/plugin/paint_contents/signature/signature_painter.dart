import 'package:flutter/material.dart';

import 'cubic_path.dart';
import 'utils.dart';

/// Type of signature path.
/// [line] - simple line with constant size.
/// [arc] - nicest, but worst performance. Creates thousands of small arcs.
/// [shape] - every part of line is created by closed path and filled. Looks good and also have great performance.
enum SignatureDrawType {
  line,
  arc,
  shape,
}

/// [CustomPainter] of [CubicPath].
/// Used during signature painting.
class PathSignaturePainter  {
  /// Paths to paint.
  final List<CubicPath> paths;

  /// Type of signature path.
  final SignatureDrawType type;


  /// Minimal size of path.
  final double width;

  /// Maximal size of path.
  final double maxWidth;

  //TODO: remove this and move size changes to Widget side..
  /// Callback when canvas size is changed.
  final bool Function(Size size)? onSize;


  Paint paint;

  /// [Path] painter.
  PathSignaturePainter({
    required this.paths,
    required this.paint,
    this.width = 2,
    this.maxWidth = 10,
    this.onSize,
    this.type=SignatureDrawType.shape,
  }){
    paint
      ..style=PaintingStyle.fill
     ;
  }


  void doPaint(Canvas canvas, Size size) {
    switch(type){
      case SignatureDrawType.line:
        paint
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..strokeJoin = StrokeJoin.round
        ..strokeWidth = width;
        for (var path in paths) {
          if (path.isFilled) {
            canvas.drawPath(PathUtil.toLinePath(path.lines), paint);
          }
        }
        break;
      case SignatureDrawType.arc:
        paint
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round
          ..strokeWidth = width;
        for (var path in paths) {
          for (var arc in path.arcs) {
            paint.strokeWidth = width + (maxWidth - width) * arc.size;
            canvas.drawPath(arc.path, paint);
          }
        }
        break;
      case SignatureDrawType.shape:


        if (onSize != null) {
          if (onSize!.call(size)) {
            return;
          }
        }

        if (paths.isEmpty) {
          return;
        }
        for (var path in paths) {
          if (path.isFilled) {
            if (path.isDot) {
              canvas.drawCircle(path.lines[0],
                  path.lines[0].startRadius(width, maxWidth), paint);
            } else {
              canvas.drawPath(
                  PathUtil.toShapePath(path.lines, width, maxWidth), paint);

              final first = path.lines.first;
              final last = path.lines.last;

              canvas.drawCircle(
                  first.start, first.startRadius(width, maxWidth), paint);
              canvas.drawCircle(
                  last.end, last.endRadius(width, maxWidth), paint);
            }
          }
        }
        break;
    }
  }

}



import 'dart:ui';

import 'package:easy_white_board/page/drawing_board/drawing_board_page_controller.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/signature/signature_painter.dart';

import '../../helper/safe_value_notifier.dart';
import '../../paint_extension/ex_paint.dart';
import '../paint_content.dart';
import 'cubic_path.dart';
import 'utils.dart';

class SignatureLineSize{
  final double width;
  final double maxWidth;

  SignatureLineSize({required this.width, required this.maxWidth});
}

/// 笔触线条
class SignatureLine extends PaintContent {

  /// Distance between two control points.
  final double threshold;

  /// Smoothing ratio of path.
  final double smoothRatio;

  /// Maximal velocity.
  final double velocityRange;

  /// Currently unfinished path.
  CubicPath? _activePath;

  /// Checks if is there unfinished path.
  bool get hasActivePath => _activePath != null;

  /// List of active paths.
  final _paths = <CubicPath>[];

  /// List of currently completed lines.
  List<CubicPath> get paths => _paths;



  /// Lazy list of all control points - raw data.
  List<List<Offset>> get _offsets {
    final list = <List<Offset>>[];

    for (var data in _paths) {
      list.add(data.points);
    }

    return list;
  }

  /// Lazy list of all Lines.
  List<List<CubicLine>> get _cubicLines {
    final list = <List<CubicLine>>[];

    for (var data in _paths) {
      list.add(data.lines);
    }

    return list;
  }

  /// Lazy list of all Arcs.
  List<CubicArc> get _arcs {
    final list = <CubicArc>[];

    for (var data in _paths) {
      list.addAll(data.arcs);
    }

    return list;
  }

  /// Lazy list of all Lines.
  List<CubicLine> get lines {
    final list = <CubicLine>[];

    for (var data in _paths) {
      list.addAll(data.lines);
    }

    return list;
  }

  SignatureLine({
    this.threshold = 0.1,
    this.smoothRatio = 0.95,
    this.velocityRange = 1.0,
  }) : assert(threshold > 0.0),
        assert(smoothRatio > 0.0 && smoothRatio <= 1.0),
        assert(velocityRange > 0.0);

  SignatureLine.data({
    required this.threshold,
    required this.smoothRatio,
    required this.velocityRange,
    required Paint paint,
  }) : super.paint(paint);

  factory SignatureLine.fromJson(Map<String, dynamic> data) {
    return SignatureLine.data(
      threshold: data['threshold'] as double,
      smoothRatio: data['smoothRatio'] as double,
      velocityRange: data['velocityRange'] as double,
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    )..importData(data);
  }

  void importData(Map data) {
    final list = <CubicPath>[];

    final paths = data['paths'];
    final threshold = data['threshold'];
    final smoothRatio = data['smoothRatio'];
    final velocityRange = data['velocityRange'];

    for (final path in paths) {
      final List points = List.from(path);

      final cp = CubicPath(
        threshold: threshold,
        smoothRatio: smoothRatio,
      )..maxVelocity = velocityRange;

      cp.begin(OffsetPoint.fromJson(points[0]));
      points.skip(1).forEach((element) => cp.add(OffsetPoint.fromJson(element)));
      cp.end();

      list.add(cp);
    }

    importPath(list);
  }

  void importPath(List<CubicPath> paths) {
    _paths.addAll(paths);
  }




  @override
  void startDraw(Offset startPoint) {
    //开始绘制
    assert(!hasActivePath);

    _activePath = CubicPath(
      threshold: threshold,
      smoothRatio: smoothRatio,
    )..maxVelocity = velocityRange;

    _activePath!.begin(startPoint,
        velocity: _paths.isNotEmpty ? _paths.last.currentVelocity : 0.0);

    _paths.add(_activePath!);
  }


  @override
  void drawing(Offset nowPoint) {
    //正在绘制
    assert(hasActivePath);

    _activePath!.add(nowPoint);
  }



  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    SignatureLineSize signatureLineSize = getLineSize();
    final PathSignaturePainter pathSignaturePainter=PathSignaturePainter(
        paths: _paths,
        paint: paint,
      type: SignatureDrawType.shape,
      width: signatureLineSize.width,
      maxWidth: signatureLineSize.maxWidth,
    );
    pathSignaturePainter.doPaint(canvas, size);
  }

  SignatureLineSize getLineSize(){
    SignatureLineSize  sgignatureSize =SignatureLineSize(width: paint.strokeWidth, maxWidth: paint.strokeWidth*5);
    return sgignatureSize;
  }


  @override
  SignatureLine copy() => SignatureLine(threshold: threshold,smoothRatio: smoothRatio,velocityRange: velocityRange);

  @override
  bool isIntersectingRectangle(Rect rect) {
    bool isIntersecting=false;
    for (var element in paths) {
      var points = element.points;
      if(_isPointsInRect(points, rect)){
        isIntersecting=true;
        break;
      }
    }
    return isIntersecting;
  }


  bool _isPointsInRect(List<OffsetPoint> points,Rect rect){
    bool isIntersecting=false;
    for (var element in points) {
      if(element.dx>rect.left && element.dx<rect.right && element.dy>rect.top && element.dy<rect.bottom){
        isIntersecting=true;
        break;
      }
    }
    return isIntersecting;
  }



  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'threshold': threshold,
      'smoothRatio': smoothRatio,
      'velocityRange': velocityRange,
      'paint': paint.toJson(),
      'paths': paths.map((e) => e.points.map((p) => p.toJson()).toList()).toList(),
    };
  }
}
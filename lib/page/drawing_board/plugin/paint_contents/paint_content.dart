import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/arrow_line.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/signature/signature_line.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/text_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';

import '../helper/safe_value_notifier.dart';
import 'circle.dart';
import 'content_eraser.dart';
import 'empty_content.dart';
import 'eraser.dart';
import 'rectangle.dart';
import 'rhombus.dart';
import 'round_rectangle.dart';
import 'simple_line.dart';
import 'smooth_line.dart';
import 'straight_line.dart';

/// 绘制对象
abstract class PaintContent {
  PaintContent():id=DateTime.now().millisecondsSinceEpoch.toString();

  PaintContent.paint(this.paint){
    id=DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 画笔
  late Paint paint;

  late String id;

  late List<PaintContent> history;
  late SafeValueNotifier<int> currentIndex;


  /// 复制实例，避免对象传递
  PaintContent copy();

  /// 绘制核心方法
  /// * [deeper] 当前是否为底层绘制
  /// * 出于性能考虑
  /// * 绘制过程为表层绘制，绘制完成抬起手指时会进行底层绘制
  void draw(Canvas canvas, Size size, bool deeper);

  /**
   * 正在绘制
   */
  void drawingWithHistory(Offset nowPoint,List<PaintContent> history, SafeValueNotifier<int> currentIndex){
    this.history=history;
    this.currentIndex=currentIndex;
    drawing(nowPoint);
  }

  /// 正在绘制
  void drawing(Offset nowPoint);

  void startDraw(Offset startPoint);

  /// 开始绘制
  void startDrawW(Offset startPoint,Offset position){
    startDraw(startPoint);
  }

  /**
   * 这个控件是否有效
   */
  Future<bool> isValidContent() async{
    return true;
  }

  //判断图形是否和指定的矩形相交
  bool isIntersectingRectangle(Rect rect){
    return false;
  }

  /// toJson
  Map<String, dynamic> toContentJson();

  /// toJson
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'type': runtimeType.toString(),
      'id':id,
      ...toContentJson(),
    };
  }

  @override
  String toString() {
    return 'PaintContent{id: $id, type:$runtimeType}';
  }
}


class PaintContentHelper{

  static PaintContent? fromJson(Map<String, dynamic> data){
       String type= data['type']??"";
       String id=data['id']??"";
       PaintContent? result;
       switch (type) {
         case "ArrowLine":
           result= ArrowLine.fromJson(data);
           break;
         case "Circle":
           result= Circle.fromJson(data);
           break;
         case "ContentEraser":
           result=ContentEraser.fromJson(data);
           break;
         case "EmptyContent":
           result= EmptyContent.fromJson(data);
           break;
         case "Eraser":
           result= Eraser.fromJson(data);
           break;
         case "Rectangle":
           result= Rectangle.fromJson(data);
           break;
         case "RoundRectangle":
           result= RoundRectangle.fromJson(data);
           break;
         case "SimpleLine":
           result= SimpleLine.fromJson(data);
           break;
         case "SmoothLine":
           result=  SmoothLine.fromJson(data);
           break;
         case "StraightLine":
           result=  StraightLine.fromJson(data);
           break;
         case "SignatureLine":
           result= SignatureLine.fromJson(data);
           break;
         case "Rhombus":
           result= Rhombus.fromJson(data);
           break;
         case "TextContent":
           result= TextContent.fromJson(data);
           break;
       }
      result?.id=id;
       return result;
  }
}


import 'dart:ui';

import 'package:easy_white_board/log/log.dart';

import 'paint_content.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';

/**
 * 圆角矩形
 */
class RoundRectangle extends PaintContent{

  RoundRectangle();

  RoundRectangle.data({
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
  }) : super.paint(paint);

  factory RoundRectangle.fromJson(Map<String, dynamic> data) {
    return RoundRectangle.data(
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }

  /// 起始点
  Offset startPoint = Offset.zero;

  /// 结束点
  Offset endPoint = Offset.zero;

  @override
  void startDraw(Offset startPoint){
    this.startPoint = startPoint;
  }

  @override
  void drawing(Offset nowPoint){
    endPoint = nowPoint;
  }

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    // canvas.drawRect(Rect.fromPoints(startPoint, endPoint), paint);
    if(endPoint.dx==0 && endPoint.dy==0){
      return;
    }
    RRect rRect=RRect.fromRectAndRadius(Rect.fromPoints(startPoint, endPoint), const Radius.circular(10));
    canvas.drawRRect(rRect, paint);
  }



  @override
  RoundRectangle copy() => RoundRectangle();

  @override
  bool isIntersectingRectangle(Rect rect) {
    Rect me=Rect.fromPoints(startPoint, endPoint);
    if (rect.left > me.right || rect.right < me.left ||
        rect.top > me.bottom || rect.bottom < me.top) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
    };
  }

}
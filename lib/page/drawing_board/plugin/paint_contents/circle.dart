import 'package:easy_white_board/log/log.dart';
import 'package:flutter/painting.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';

import 'paint_content.dart';

/// 圆
class Circle extends PaintContent {
  Circle({
    this.isEllipse = false,
    this.startFromCenter = true,
  });

  Circle.data({
    this.isEllipse = false,
    this.startFromCenter = true,
    required this.center,
    required this.radius,
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
  }) : super.paint(paint);

  factory Circle.fromJson(Map<String, dynamic> data) {
    return Circle.data(
      isEllipse: data['isEllipse'] as bool,
      startFromCenter: data['startFromCenter'] as bool,
      center: jsonToOffset(data['center'] as Map<String, dynamic>),
      radius: data['radius'] as double,
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }

  /// 是否为椭圆
  final bool isEllipse;

  /// 从圆心开始绘制
  final bool startFromCenter;

  /// 圆心
  Offset center = Offset.zero;

  /// 半径
  double radius = 0;

  /// 起始点
  Offset startPoint = Offset.zero;

  /// 结束点
  Offset endPoint = Offset.zero;

  @override
  void startDraw(Offset startPoint) {
    this.startPoint = startPoint;
    center = startPoint;
  }

  @override
  void drawing(Offset nowPoint) {
    endPoint = nowPoint;
    center = Offset(
        (startPoint.dx + endPoint.dx) / 2, (startPoint.dy + endPoint.dy) / 2);
    radius = (endPoint - (startFromCenter ? startPoint : center)).distance;
  }

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    if (isEllipse) {
      canvas.drawOval(Rect.fromPoints(startPoint, endPoint), paint);
    } else {
      canvas.drawCircle(startFromCenter ? startPoint : center, radius, paint);
    }
  }

  @override
  Circle copy() => Circle(isEllipse: isEllipse);

  @override
  bool isIntersectingRectangle(Rect rect) {
    if(isEllipse){
      Rect ellipseRect= Rect.fromPoints(startPoint, endPoint);
      double ellipseRadiusX = ellipseRect.width / 2;
      double ellipseRadiusY = ellipseRect.height / 2;

      double ellipseCenterX = ellipseRect.center.dx;
      double ellipseCenterY = ellipseRect.center.dy;

      double rectLeft = ellipseRect.left;
      double rectRight = ellipseRect.right;
      double rectTop = ellipseRect.top;
      double rectBottom = ellipseRect.bottom;

      double closestX = ellipseCenterX.clamp(rectLeft, rectRight);
      double closestY = ellipseCenterY.clamp(rectTop, rectBottom);

      double distanceX = ellipseCenterX - closestX;
      double distanceY = ellipseCenterY - closestY;

      double distanceSquared = (distanceX * distanceX) / (ellipseRadiusX * ellipseRadiusX) +
          (distanceY * distanceY) / (ellipseRadiusY * ellipseRadiusY);
      return distanceSquared <= 1;
    }else{
      Offset circleCenter=startFromCenter?startPoint:center;
      // 计算圆心到矩形的最近点的水平和垂直距离
      double closestX = circleCenter.dx.clamp(rect.left, rect.right);
      double closestY = circleCenter.dy.clamp(rect.top, rect.bottom);

      // 计算圆心到矩形的最近点的距离
      double distanceX = circleCenter.dx - closestX;
      double distanceY = circleCenter.dy - closestY;
      double distanceSquared = distanceX * distanceX + distanceY * distanceY;
      // 判断圆和矩形是否相交
      return distanceSquared <= radius * radius;
    }
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'isEllipse': isEllipse,
      'startFromCenter': startFromCenter,
      'center': center.toJson(),
      'radius': radius,
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
    };
  }
}

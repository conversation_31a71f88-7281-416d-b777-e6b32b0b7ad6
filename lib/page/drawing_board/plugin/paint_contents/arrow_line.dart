import 'package:flutter/painting.dart';
import '../draw_path/port_path/arrow_path.dart';
import '../draw_path/port_path/port_path.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';

import 'paint_content.dart';

/// 箭头
class ArrowLine extends PaintContent {

  final double arrowSize=5;

  ArrowLine();

  ArrowLine.data({
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
  }) : super.paint(paint);

  factory ArrowLine.fromJson(Map<String, dynamic> data) {
    return ArrowLine.data(
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }

  Offset startPoint = Offset.zero;
  Offset endPoint = Offset.zero;

  @override
  void startDraw(Offset startPoint) => this.startPoint = startPoint;

  @override
  void drawing(Offset nowPoint) => endPoint = nowPoint;

  @override
  void draw(Canvas canvas, Size size, bool deeper){
    if(endPoint.dx==0 && endPoint.dy==0){
      return;
    }
    int distance=1;
    double xabs=(endPoint.dx-startPoint.dx).abs();
    double yabs=(endPoint.dy-startPoint.dy).abs();
    double width=xabs>yabs?xabs:yabs;
    if(width>distance){
      ArrowPath arrow = ArrowPath(
        head: PortPath(
            startPoint ,
            Size(arrowSize, arrowSize),
        ),
        tail: PortPath(
        endPoint,
          Size(arrowSize, arrowSize),
            portPath: ThreeAnglePortPath(),
      ),

      );
    canvas.drawPath(arrow.formPath(),paint);
    }

  }

  @override
  ArrowLine copy() => ArrowLine();

  @override
  bool isIntersectingRectangle(Rect rect) {
    Rect me=Rect.fromPoints(startPoint, endPoint);
    if (rect.left > me.right || rect.right < me.left ||
        rect.top > me.bottom || rect.bottom < me.top) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
    };
  }
}

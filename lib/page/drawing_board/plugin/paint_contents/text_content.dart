import 'dart:ui';

import 'package:easy_white_board/log/log.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/paint_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../drawing_board_page_controller.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';

/// 文字控件
class TextContent extends PaintContent {

  final TextEditingController _controller = TextEditingController();

  final FocusNode _focusNode=FocusNode();

  String text = "";

  double fontSize=12;

  /// 起始点
  Offset startPoint = Offset.zero;

  Offset inputPosition = Offset.zero;

  /// 结束点
  Offset endPoint = Offset.zero;

  TextContent();

  late TextStyle textStyle = TextStyle(color: paint.color,fontSize: fontSize+paint.strokeWidth);

  TextContent.data({
    required this.text,
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
  }) : super.paint(paint);

  factory TextContent.fromJson(Map<String, dynamic> data) {
    return TextContent.data(
      text: data['text'],
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }



  @override
  PaintContent copy() => TextContent();

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    if(deeper && text.isNotEmpty){
     _drawText(canvas);
    }
  }

  void _drawText(Canvas canvas){
    var textPainter = TextPainter(
      text: TextSpan(
          text: text,
          style: textStyle),
      textDirection: TextDirection.ltr,
      textWidthBasis: TextWidthBasis.longestLine,
    )
      ..layout()
    ;
    textPainter.paint(canvas, startPoint);
  }

  @override
  void startDraw(Offset startPoint){
  }

  @override
  void startDrawW(Offset startPoint,Offset position) {
    this.startPoint = startPoint;
    inputPosition=position;
  }

  @override
  void drawing(Offset nowPoint) {
    endPoint = nowPoint;
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
      'text': text,
    };
  }

  @override
  Future<bool> isValidContent() async {
    await getInputText(inputPosition);
    return text.isNotEmpty;
  }

  Future<String> getInputText(Offset position) async {
    await SmartDialog.showAttach(
        usePenetrate: false,
        builder: (c) {
          _focusNode.requestFocus();
          return Container(
            // color: Colors.grey,
            margin: EdgeInsets.only(left: position.dx),
            child: TextField(
              controller: _controller,
              autofocus: true,
              focusNode: _focusNode,
              style: textStyle,
              textDirection: TextDirection.ltr,
              maxLines: 100,
              decoration: const InputDecoration(
                border: InputBorder.none,
              ),
            ),
          );
        },
        targetContext: null,
        targetBuilder: (Offset targetOffset, Size targetSize) {
          // return Offset(x, y);
          return position;
        },
        maskColor: Colors.transparent,
        backDismiss: true,
        clickMaskDismiss: true,
        keepSingle: true,
        onDismiss: (){
          text=_controller.text;
        }
    );
    return text;
  }
}

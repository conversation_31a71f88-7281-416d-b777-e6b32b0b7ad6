

import 'dart:ui';

import 'package:easy_white_board/log/log.dart';

import 'paint_content.dart';
import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';

/**
 * 圆角矩形
 */
class Rhombus extends PaintContent{

  Rhombus();

  Rhombus.data({
    required this.startPoint,
    required this.endPoint,
    required Paint paint,
  }) : super.paint(paint);

  factory Rhombus.fromJson(Map<String, dynamic> data) {
    return Rhombus.data(
      startPoint: jsonToOffset(data['startPoint'] as Map<String, dynamic>),
      endPoint: jsonToOffset(data['endPoint'] as Map<String, dynamic>),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }

  /// 起始点
  Offset startPoint = Offset.zero;

  /// 结束点
  Offset endPoint = Offset.zero;

  @override
  void startDraw(Offset startPoint){
    this.startPoint = startPoint;
  }

  @override
  void drawing(Offset nowPoint){
    endPoint = nowPoint;
  }

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    // canvas.drawRect(Rect.fromPoints(startPoint, endPoint), paint);
    if(endPoint.dx==0 && endPoint.dy==0){
      return;
    }

    // RRect rRect=RRect.fromRectAndRadius(Rect.fromPoints(startPoint, endPoint), const Radius.circular(10));
    // canvas.drawRRect(rRect, paint);
    var path = Path();

    if(startPoint.dx<endPoint.dx && startPoint.dy< endPoint.dy){
      /// 第一个点

      path.moveTo((startPoint.dx + (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);

      /// 第二个点

      path.lineTo(startPoint.dx, startPoint.dy + ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 第三个点

      path.lineTo(startPoint.dx + ((endPoint.dx - startPoint.dx) / 2).abs(), endPoint.dy);

      /// 第四个点

      path.lineTo(endPoint.dx, startPoint.dy + ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 回到起点

      path.lineTo((startPoint.dx + (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);
    }else if(startPoint.dx > endPoint.dx && startPoint.dy<endPoint.dy){
      /// 第一个点

      path.moveTo((startPoint.dx - (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);

      /// 第二个点

      path.lineTo(startPoint.dx, startPoint.dy + ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 第三个点

      path.lineTo(startPoint.dx - ((endPoint.dx - startPoint.dx) / 2).abs(), endPoint.dy);

      /// 第四个点

      path.lineTo(endPoint.dx, startPoint.dy + ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 回到起点

      path.lineTo((startPoint.dx - (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);
    }else if(startPoint.dx<endPoint.dx && startPoint.dy>endPoint.dy){
      /// 第一个点

      path.moveTo((startPoint.dx + (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);

      /// 第二个点

      path.lineTo(startPoint.dx, startPoint.dy - ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 第三个点

      path.lineTo(startPoint.dx + ((endPoint.dx - startPoint.dx) / 2).abs(), endPoint.dy);

      /// 第四个点

      path.lineTo(endPoint.dx, startPoint.dy - ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 回到起点

      path.lineTo((startPoint.dx + (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);
    }else{
      /// 第一个点

      path.moveTo((startPoint.dx - (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);

      /// 第二个点

      path.lineTo(startPoint.dx, startPoint.dy - ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 第三个点

      path.lineTo(startPoint.dx - ((endPoint.dx - startPoint.dx) / 2).abs(), endPoint.dy);

      /// 第四个点

      path.lineTo(endPoint.dx, startPoint.dy - ((endPoint.dy - startPoint.dy) / 2).abs());

      /// 回到起点

      path.lineTo((startPoint.dx - (startPoint.dx - endPoint.dx).abs() / 2).abs(), startPoint.dy);
    }


    canvas.drawPath(path, paint);
  }



  @override
  Rhombus copy() => Rhombus();

  @override
  bool isIntersectingRectangle(Rect rect) {
    Rect me=Rect.fromPoints(startPoint, endPoint);
    if (rect.left > me.right || rect.right < me.left ||
        rect.top > me.bottom || rect.bottom < me.top) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'startPoint': startPoint.toJson(),
      'endPoint': endPoint.toJson(),
      'paint': paint.toJson(),
    };
  }

}
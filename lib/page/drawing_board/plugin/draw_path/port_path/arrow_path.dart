import 'dart:math';
import 'dart:ui';

import 'package:flutter/cupertino.dart';

import 'abstract_path.dart';
import 'port_path.dart';

class ArrowPath extends AbstractPath{
  final PortPath head;
  final PortPath tail;
  final double height;

  ArrowPath({required this.head,required this.tail, this.height=0.2});

  @override
  Path formPath() {
    Offset line = (tail.position - head.position);
    Offset center = head.position+line/2;
    double length = line.distance-head.size.width;
    Rect lineZone = Rect.fromCenter(center:center,width:length,height:height/2);
    Path linePath = Path()..addRect(lineZone);


    // 通过矩阵变换，让 linePath 以 center 为中心旋转 两点间角度
    Matrix4 lineM4 = Matrix4.translationValues(center.dx, center.dy, 0); // tag1
    lineM4.multiply(Matrix4.rotationZ(line.direction));
    lineM4.multiply(Matrix4.translationValues(-center.dx, -center.dy, 0)); // tag2
    linePath = linePath.transform(lineM4.storage);

    double fixDx = head.size.width/2*cos(line.direction);
    double fixDy = head.size.height/2*sin(line.direction);

    Path headPath = head.formPath();
    Matrix4 headM4 = Matrix4.translationValues(fixDx, fixDy, 0);
    center = head.position;
    headM4.multiply(Matrix4.translationValues(center.dx, center.dy, 0));
    headM4.multiply(Matrix4.rotationZ(line.direction));
    headM4.multiply(Matrix4.translationValues(-center.dx, -center.dy, 0));
    headPath = headPath.transform(headM4.storage);



    Matrix4 tailM4 = Matrix4.translationValues(-fixDx, -fixDy, 0);
    center = tail.position;
    tailM4.multiply(Matrix4.translationValues(center.dx, center.dy, 0));
    tailM4.multiply(Matrix4.rotationZ(line.direction-pi)); // tag3
    tailM4.multiply(Matrix4.translationValues(-center.dx, -center.dy, 0));
    Path tailPath = tail.formPath();
    tailPath = tailPath.transform(tailM4.storage);


    Path temp = Path.combine(PathOperation.union, linePath, headPath);
    return Path.combine(PathOperation.union, temp, tailPath);
  }

}


class ThreeAnglePortPath extends PortPathBuilder{
  final double rate;

  ThreeAnglePortPath({this.rate = 0.2});

  @override
  Path fromPathByRect(Rect zone) {
    Path path = Path();
    Offset p0 = zone.centerLeft;
    Offset p1 = zone.bottomRight;
    Offset p2 = zone.topRight;
    Offset p3 = p0.translate(rate * zone.width, 0);
    path
      ..moveTo(p0.dx, p0.dy)
      ..lineTo(p1.dx, p1.dy)
      ..lineTo(p3.dx, p3.dy)
      ..lineTo(p2.dx, p2.dy)
      ..close();
    return path;
  }
}

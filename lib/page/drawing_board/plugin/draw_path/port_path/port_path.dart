
import 'dart:ui';

import 'package:easy_white_board/page/drawing_board/plugin/draw_path/port_path/abstract_path.dart';

abstract class PortPathBuilder{
  const PortPathBuilder();
  Path fromPathByRect(Rect zone);
}

class PortPath extends AbstractPath {
  final Offset position;
  final Size size;
  PortPathBuilder portPath;

  PortPath(
      this.position,
      this.size, {
        this.portPath = const CustomPortPath(),
      });

  @override
  Path formPath() {
    Rect zone = Rect.fromCenter(
        center: position, width: size.width, height: size.height);
    return portPath.fromPathByRect(zone);
  }
}

class CustomPortPath extends PortPathBuilder{
  const CustomPortPath();

  @override
  Path fromPathByRect(Rect zone) {
    Path path = Path();
    return path;
  }
}


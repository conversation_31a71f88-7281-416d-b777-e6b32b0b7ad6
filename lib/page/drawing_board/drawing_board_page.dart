import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/log/log.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/content_eraser.dart';
import 'package:easy_white_board/provider/business/bg_provider.dart';
import 'package:easy_white_board/utils/image_helper.dart';
import 'package:easy_white_board/utils/ui_util.dart';
import 'package:easy_white_board/widgets/common/base_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'drawing_board_page_controller.dart';
import 'plugin/drawing_board.dart';
import 'plugin/helper/ex_value_builder.dart';
import 'plugin/paint_contents/circle.dart';
import 'plugin/paint_contents/round_rectangle.dart';

class DrawingBoardPage extends ConsumerStatefulWidget {
  final int? id;

  const DrawingBoardPage({super.key, this.id});

  @override
  ConsumerState createState() {
    return _DrawingBoardPageState();
  }
}

class _DrawingBoardPageState extends ConsumerState<DrawingBoardPage>
    with WidgetsBindingObserver {
  late DrawingBoardPageController _controller;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        Logger.info('应用程序可见并响应用户输入。');
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight
        ]);
        break;
      case AppLifecycleState.inactive:
        Logger.info('应用程序处于非活动状态，并且未接收用户输入');
        _controller.onInactive(context);
        break;
      case AppLifecycleState.paused:
        Logger.info('用户当前看不到应用程序，没有响应');
        break;
      case AppLifecycleState.detached:
        Logger.info('detached');
        break;
      default:
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    // 添加监听
    _controller = DrawingBoardPageController(ref: ref, setState: setState);
    _controller.initDrawingData(widget.id);
    super.initState();
  }

  @override
  void deactivate() {
    super.deactivate();
    Logger.info("========= deactivate");
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
    _controller.disposeController();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, result) async {
        if (!didPop) {
          showLoading(msg: context.string.onSaving);
          await _controller
              .saveDrawingData()
              .whenComplete(() => dismissLoading());
          SystemNavigator.pop();
        }
      },
      child: BaseScaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            _drawingBoard(),
            _actionButtons(context),
            _toHomeButton(),
            _pageTitle(),
          ],
        ),
      ),
    );
  }

  Widget _actionButtons(BuildContext context) {
    return Positioned(
        top: MediaQuery.of(context).padding.top + 50.h,
        child: Container(
          margin: EdgeInsets.symmetric(
            horizontal: 15.w,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              InkWell(
                  onTap: () {
                    _controller.drawingController.clear();
                    setState(() {});
                  },
                  child: Image.asset(
                    ImageHelper.wrapAssets("clear.png"),
                    width: 23.r,
                    fit: BoxFit.fitWidth,
                  )),
              ExValueBuilder<int>(
                valueListenable: _controller.drawingController.currentIndex,
                builder: (BuildContext context, int value, Widget? child) {
                  return IconButton(
                      icon: const Icon(CupertinoIcons.arrow_turn_up_left),
                      color: value > 0 ? Colors.blue : null,
                      onPressed: () {
                        _controller.drawingController.undo();
                        Logger.info(
                            "history: ${_controller.drawingController.getHistory}");
                        setState(() {});
                      });
                },
              ),
              ExValueBuilder<int>(
                valueListenable: _controller.drawingController.currentIndex,
                builder: (BuildContext context, int value, Widget? child) {
                  return IconButton(
                      icon: const Icon(CupertinoIcons.arrow_turn_up_right),
                      color: value <
                              _controller.drawingController.getHistory.length
                          ? Colors.blue
                          : null,
                      onPressed: () {
                        _controller.drawingController.redo();
                        setState(() {});
                      });
                },
              ),
              IconButton(
                  icon: const Icon(CupertinoIcons.scope),
                  onPressed: () => _controller.toCenter()),
              // IconButton(
              //     icon: const Icon(CupertinoIcons.rectangle_split_3x3),
              //     onPressed: ()=>_controller.showBgConfigMenu()),
              // IconButton(
              //     icon: const Icon(CupertinoIcons.arrow_down_to_line_alt),
              //     onPressed: ()=>_controller.doExport(context)),
            ],
          ),
        ));
  }

  Widget _toHomeButton() {
    return Positioned(
      width: 1.sw,
      top: MediaQuery.of(context).padding.top + 20.h,
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(
          horizontal: 15.w,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                color: Colors.white,
              ),
              child: IconButton(
                icon: const Icon(CupertinoIcons.home),
                onPressed: () {
                  _controller.toHome(context);
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _pageTitle() {
    return Positioned(
      width: 1.sw,
      top: MediaQuery.of(context).padding.top + 30.h,
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(
          horizontal: 15.w,
        ),
        child:
            Text(_controller.data != null ? _controller.data!.name ?? "" : ""),
      ),
    );
  }

  Widget _drawingBoard() {
    double size = 25.r;
    return OrientationBuilder(builder: (_, orientation) {
      Logger.info("======== orientation: $orientation");
      _controller.initBoardOrientation(orientation);
      return RepaintBoundary(
        key: _controller.boardKey,
        child: DrawingBoard(
          boardPanEnabled: false,
          boardScaleEnabled: true,
          boardBoundaryMargin: EdgeInsets.zero,
          maxScale: BoarderConfig.zoomFactor,
          minScale: BoarderConfig.zoomFactor / BoarderConfig.bgSizeFactor,
          controller: _controller.drawingController,
          background: Consumer(
            builder: (c, ref, child) {
              BgData bgData = ref.watch(bgProvider);
              var bgNotifier = ref.read(bgProvider.notifier);
              Logger.info(
                  "========bgData: ${bgData.bgType}, ${bgData.bgColor.toString()} ");
              return bgNotifier.assembleBg(bgData);
            },
          ),
          transformationController: _controller.transformationController,
          showDefaultActions: false,
          showDefaultTools: true,
          defaultToolsBuilder: (c, currType, controller) => [
            DefToolItem(
                isActive: _controller.isPen(currType),
                image: ImageHelper.wrapAssets("pen.png"),
                iconSize: size,
                onTap: () {
                  _controller.setPen(c, currType);
                }),
            // DefToolItem(
            //     isActive: currType == StraightLine,
            //     image: ImageHelper.wrapAssets("line.png"),
            //     iconSize: size,
            //     onTap: () => _controller.setPaintContent(
            //         c, currType, StraightLine(), currType == StraightLine)),
            // DefToolItem(
            //     isActive: currType == ArrowLine,
            //     icon: Icons.arrow_right_alt,
            //     onTap: () => _controller.setPaintContent(c,currType,ArrowLine(),currType==ArrowLine)),
            DefToolItem(
                isActive: currType == RoundRectangle,
                icon: CupertinoIcons.stop,
                iconSize: size,
                onTap: () => _controller.setPaintContent(
                    c, currType, RoundRectangle(), currType == RoundRectangle)),
            // DefToolItem(
            //     isActive: currType == Rhombus,
            //     icon: CupertinoIcons.rhombus,
            //     onTap: () => _controller.setPaintContent(c,currType,Rhombus(),currType==Rhombus)),
            DefToolItem(
                isActive: currType == Circle,
                icon: CupertinoIcons.circle,
                iconSize: size,
                onTap: () => _controller.setPaintContent(
                    c, currType, Circle(isEllipse: true), currType == Circle)),
            // DefToolItem(
            //     isActive: currType == TextContent,
            //     icon: CupertinoIcons.textformat_size,
            //     onTap: ()=>_controller.setPaintContent(c, currType, TextContent(), currType == TextContent)),
            DefToolItem(
                isActive: currType == ContentEraser,
                image: ImageHelper.wrapAssets("eraser.png"),
                iconSize: size,
                onTap: () => controller.setPaintContent(ContentEraser())),
          ],
        ),
      );
    });
  }
}

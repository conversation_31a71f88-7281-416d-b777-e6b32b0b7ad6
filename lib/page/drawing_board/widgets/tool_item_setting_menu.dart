import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/page/drawing_board/drawing_board_page_controller.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/paint_content.dart';
import 'package:easy_white_board/utils/ui_util.dart';
import 'package:easy_white_board/widgets/seekbar/seekbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../plugin/drawing_controller.dart';
import '../plugin/helper/safe_value_notifier.dart';
import '../plugin/paint_contents/signature/signature_line.dart';
import '../plugin/paint_contents/simple_line.dart';

class ToolItemSettingMenu extends StatefulWidget {
  final DrawingBoardPageController controller;
  final PaintContent content;
  final List<Widget>? menus;

  const ToolItemSettingMenu(
      {super.key, required this.controller, required this.content, this.menus});

  @override
  State<StatefulWidget> createState() {
    return _ToolItemSettingMenuState();
  }
}

class _ToolItemSettingMenuState extends State<ToolItemSettingMenu> {
  late SafeValueNotifier<DrawConfig> config;

  @override
  void initState() {
    super.initState();
    config = widget.controller.drawingController.drawConfig;
  }

  List<Widget> getMenus() {
    if (widget.menus != null) {
      return widget.menus!;
    } else {
      List<Widget> children = [];
      if (widget.controller.isPen(config.value.contentType)) {
        children.add(_getPenExtra(config.value.contentType));
      }
      var style =
          widget.controller.loadPainterStyle(widget.content.runtimeType);
      children.addAll([
        SizeMenu(
          onSizeChange: (double size) {
            widget.controller.updateSizeChange(widget.content, size);
          },
          size: style.strokeWidth,
        ),
        Container(
          width: 1.w,
          height: 20.w,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          color: const Color(0xfff1f0fe).withValues(alpha: 0.4),
        ),
        ColorMenu(
            color: style.color,
            onColorChange: (Color color) {
              widget.controller.updateColorChange(widget.content, color);
            }),
      ]);
      return children;
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = getMenus();
    return Container(
      padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 30.h, bottom: 30.h),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.w), topRight: Radius.circular(10.w))),
      width: 1.sw,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: children,
        ),
      ),
    );
  }

  Widget _getPenExtra(Type currType) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 1.w,
          height: 20.w,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          color: const Color(0xfff1f0fe).withValues(alpha: 0.4),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: 18.w,
          height: 18.w,
          child: Checkbox(
              value: currType == SignatureLine,
              onChanged: (bool? value) {
                if (value != null) {
                  if (value && currType != SignatureLine) {
                    widget.controller.updatePenChange(SignatureLine());
                    setState(() {});
                  } else if (!value && currType == SignatureLine) {
                    widget.controller.updatePenChange(SimpleLine());
                    setState(() {});
                  }
                }
              }),
        ),
        Text(
          context.string.handWriting,
          style: TextStyle(fontSize: 12.sp),
        )
      ],
    );
  }
}

class SizeMenu extends StatefulWidget {
  final Function(double size) onSizeChange;
  final double size;

  const SizeMenu({super.key, required this.onSizeChange, required this.size});

  @override
  State<SizeMenu> createState() => _SizeMenuState();
}

class _SizeMenuState extends State<SizeMenu> {
  late double size;

  static const double minSize = 1.0;
  static const double maxSize = 10;

  @override
  void initState() {
    size = widget.size;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(context.string.strokeWidth),
              Text("$size"),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: AdvancedSeekBar(
              Colors.grey,
              10,
              Colors.blue,
              fillProgress: true,
              defaultProgress: (size * 10).round(),
              minProgress: (minSize * 10).round(),
              maxProgress: (maxSize * 10).round(),
              percentSplitColor: Colors.black,
              seekBarFinished: (int progress) {
                widget.onSizeChange(getSize(progress));
              },
              seekBarProgress: (int progress) {
                size = getSize(progress);
                setState(() {});
              },
            ),
          ),
        ],
      ),
    );
  }

  double getSize(int progress) {
    return progress / 10;
  }
}

class ColorMenu extends StatefulWidget {
  final Function(Color color) onColorChange;
  final Color color;

  const ColorMenu(
      {super.key, required this.color, required this.onColorChange});

  @override
  State<ColorMenu> createState() => _ColorMenuState();
}

class _ColorMenuState extends State<ColorMenu> {
  late Color color;

  final double size = 18.w;

  List<Color> colors = [
    Colors.black,
    Colors.red,
    Colors.green,
    Colors.blue,
    Colors.orange,
    Colors.grey,
    Colors.yellow,
    Colors.brown,
    Colors.white,
  ];

  @override
  void initState() {
    super.initState();
    color = widget.color;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    for (Color item in colors) {
      children.add(InkWell(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: color.toString() == item.toString() ? size + 3.w : size,
          height: color.toString() == item.toString() ? size + 3.w : size,
          decoration: BoxDecoration(
              //背景颜色
              color: item,
              borderRadius: BorderRadius.circular(4.w),
              //圆角半径
              border: color.toString() == item.toString()
                  ? Border.all(
                      color: Colors.blue,
                    )
                  : (item.toString() == Colors.transparent.toString() ||
                          item.toString() == Colors.white.toString())
                      ? Border.all(color: Colors.black)
                      : null),
        ),
        onTap: () {
          color = item;
          setState(() {});
          widget.onColorChange(color);
        },
      ));
    }
    children.addAll([
      Container(
        width: 1.w,
        height: size,
        margin: EdgeInsets.symmetric(horizontal: 4.w),
        color: const Color(0xfff1f0fe).withValues(alpha: 0.4),
      ),
      InkWell(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: size,
          height: size,
          decoration: BoxDecoration(
              //背景颜色
              color: color,
              borderRadius: BorderRadius.circular(4.w),
              border: Border.all(
                color: color.computeLuminance() < 0.5
                    ? Colors.white
                    : Colors.black,
              )
              //圆角半径
              ),
        ),
        onTap: () {
          showSmartDialog(
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                child: SingleChildScrollView(
                  child: ColorPicker(
                    pickerColor: color,
                    onColorChanged: (Color value) {
                      color = value;
                      setState(() {});
                      widget.onColorChange(color);
                    },
                  ),
                ),
              ),
              backDismiss: true,
              clickMaskDismiss: true);
        },
      )
    ]);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}

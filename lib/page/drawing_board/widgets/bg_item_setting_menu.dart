import 'package:easy_white_board/page/drawing_board/drawing_board_page_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../utils/ui_util.dart';

class BgItemSettingMenu extends StatefulWidget {
  final DrawingBoardPageController controller;

  const BgItemSettingMenu({
    super.key,
    required this.controller,
  });

  @override
  State<StatefulWidget> createState() {
    return _BgItemSettingMenuState();
  }
}

class _BgItemSettingMenuState extends State<BgItemSettingMenu> {
  @override
  void initState() {
    super.initState();
  }

  List<Widget> getMenus() {
    List<Widget> children = [
      BgMenu(
          onBgChange: (String bg) {
            widget.controller.updateBg(bg, null);
          },
          currentBg: widget.controller.loadDefaultBoarderBg().bgType),
      ColorMenu(
          color: widget.controller.loadDefaultBoarderBg().bgColor,
          onColorChange: (Color color) {
            widget.controller.updateBg(null, color);
          }),
    ];

    return children;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = getMenus();
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 30.h,
        bottom: 100.h,
      ),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.w), topRight: Radius.circular(10.w))),
      width: 1.sw,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: children,
        ),
      ),
    );
  }
}

class BgMenu extends StatefulWidget {
  final String currentBg;

  final Function(String bg) onBgChange;

  const BgMenu({super.key, required this.onBgChange, required this.currentBg});

  @override
  State<BgMenu> createState() => _BgMenuState();
}

class _BgMenuState extends State<BgMenu> {
  late String bg;

  @override
  void initState() {
    super.initState();
    bg = widget.currentBg;
  }

  final List<String> bgs = [
    "NormalBg",
    "LineBg",
  ];

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            bg = bgs[0];
            widget.onBgChange(bg);
            setState(() {});
          },
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Icon(
              Icons.rectangle_outlined,
              color: bg == bgs[0] ? Colors.blue : null,
            ),
          ),
        ),
        InkWell(
          onTap: () {
            bg = bgs[1];
            widget.onBgChange(bg);
            setState(() {});
          },
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Icon(
              Icons.grid_4x4,
              color: bg == bgs[1] ? Colors.blue : null,
            ),
          ),
        ),
        // InkWell(
        //   onTap: (){
        //     bg=bgs[2];
        //     widget.onBgChange(bg);
        //     setState(() {
        //     });
        //   },
        //   child: Padding(padding: const EdgeInsets.all(10),child: Icon(Icons.grid_view,color: bg==bgs[2]?Colors.blue:null,),),
        // ),
      ],
    );
  }
}

class ColorMenu extends StatefulWidget {
  final Function(Color color) onColorChange;
  final Color color;

  const ColorMenu(
      {super.key, required this.color, required this.onColorChange});

  @override
  State<ColorMenu> createState() => _ColorMenuState();
}

class _ColorMenuState extends State<ColorMenu> {
  late Color color;

  final double size = 18.w;

  List<Color> colors = [
    Colors.transparent,
    Colors.black,
    Colors.red,
    Colors.green,
    Colors.blue,
    Colors.orange,
    Colors.grey,
    Colors.yellow,
    Colors.brown,
    Colors.white,
  ];

  @override
  void initState() {
    super.initState();
    color = widget.color;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    for (Color item in colors) {
      children.add(InkWell(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: color.toString() == item.toString() ? size + 3.w : size,
          height: color.toString() == item.toString() ? size + 3.w : size,
          decoration: BoxDecoration(
              //背景颜色
              color: item,
              borderRadius: BorderRadius.circular(4.w),
              //圆角半径
              border: color.toString() == item.toString()
                  ? Border.all(
                      color: Colors.blue,
                    )
                  : (item.toString() == Colors.transparent.toString() ||
                          item.toString() == Colors.white.toString())
                      ? Border.all(color: Colors.black)
                      : null),
        ),
        onTap: () {
          color = item;
          setState(() {});
          widget.onColorChange(color);
        },
      ));
    }
    children.addAll([
      Container(
        width: 1.w,
        height: size,
        margin: EdgeInsets.symmetric(horizontal: 4.w),
        color: const Color(0xfff1f0fe).withValues(alpha: 0.4),
      ),
      InkWell(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: size,
          height: size,
          decoration: BoxDecoration(
              //背景颜色
              color: color,
              borderRadius: BorderRadius.circular(4.w),
              border: Border.all(
                color: color.computeLuminance() < 0.5
                    ? Colors.white
                    : Colors.black,
              )
              //圆角半径
              ),
        ),
        onTap: () {
          showSmartDialog(
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                child: SingleChildScrollView(
                  child: ColorPicker(
                    pickerColor: color,
                    onColorChanged: (Color value) {
                      color = value;
                      setState(() {});
                      widget.onColorChange(color);
                    },
                  ),
                ),
              ),
              backDismiss: true,
              clickMaskDismiss: true);
        },
      )
    ]);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}

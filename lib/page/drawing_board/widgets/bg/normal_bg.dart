

import 'package:easy_white_board/log/log.dart';
import 'package:easy_white_board/page/drawing_board/widgets/bg/boarder_bg.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../drawing_board_page_controller.dart';



 
class NormalBg extends BoarderBg{



    const NormalBg({super.key, required super.bgColor,   super.lineColor});






  @override
  Widget build(BuildContext context) {
    Logger.info("========NormalBg: ${bgColor.toString()} ");
    return Center(
      child: Container(
        width: BoarderConfig.bgWidth,
        height: BoarderConfig.bgHeight,
        color: bgColor,
      ),
    );
  }

}

class LinePainter extends CustomPainter {
  final Color bgColor;
  final Color lineColor;

  final int widthCounts=45;
  final int heightCounts=65;

  LinePainter({required this.bgColor, required this.lineColor});

  @override
  void paint(Canvas canvas, Size size) {
    double eWidth = size.width / widthCounts;
    double eHeight = size.height / heightCounts;

    //網格背景
    var paint = Paint()
      ..isAntiAlias = true
      ..style = PaintingStyle.fill //填充
      ..color = bgColor
    ; //背景为纸黄色
    canvas.drawRect(Offset.zero & size, paint);

    //網格風格
    paint
      ..style = PaintingStyle.stroke //线
      ..color = lineColor
      ..strokeWidth = 1.1;

    for (int i = 0; i <= heightCounts; ++i) {
      double dy = eHeight * i;
      canvas.drawLine(Offset(0, dy), Offset(size.width, dy), paint);
    }

    for (int i = 0; i <= widthCounts; ++i) {
      double dx = eWidth * i;
      canvas.drawLine(Offset(dx, 0), Offset(dx, size.height), paint);
    }



  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
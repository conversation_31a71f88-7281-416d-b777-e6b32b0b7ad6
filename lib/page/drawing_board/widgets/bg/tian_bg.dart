import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../drawing_board_page_controller.dart';
import 'boarder_bg.dart';

/**
 * 田字格背景
 */
class TianBg extends BoarderBg {


    const TianBg({super.key,required super.bgColor,super.lineColor});






  @override
  Widget build(BuildContext context) {
    return Center(
      child: CustomPaint(
        size: Size(BoarderConfig.bgWidth, BoarderConfig.bgHeight),
        painter: LinePainter(bgColor: bgColor, lineColor: lineColor??Colors.grey),
      ),
    );
  }
}

class LinePainter extends CustomPainter {
  final Color bgColor;
  final Color lineColor;

  final int widthCounts = 10;

  LinePainter({required this.bgColor, required this.lineColor});

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }

  @override
  void paint(Canvas canvas, Size size) {
    //绘制田字格核心代码
    final outerPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = lineColor
      ..strokeWidth = 2;
    final innerPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = lineColor.withValues(alpha: 0.5)
      ..strokeWidth = 1;

    //田字格的宽度
    double width = size.width / widthCounts;
    int heightCounts = (size.height / width).ceil();
    //横线
    for(int i=0; i<widthCounts; i++){
      double startX = i * width;
      double startInnerX=i*width+width/2;
      canvas.drawLine(Offset(startX, 0), Offset(startX, size.height), outerPaint);
      canvas.drawLine(Offset(startInnerX, 0), Offset(startInnerX, size.height), innerPaint);
    }
    //竖线
    for(int j =0;j<heightCounts;j++){
      double startY = j * width;
      double startInnerY=j*width+width/2;
      canvas.drawLine(Offset(0, startY), Offset(size.width, startY), outerPaint);
      canvas.drawLine(Offset(0, startInnerY), Offset(size.width, startInnerY), innerPaint);
    }


    //右斜线
    for(int j=0;j<heightCounts+widthCounts-1;j++){
      //从左往右画
      //起点总是在最左边
      double startX=0;
      double startY=(j+1)*width;
      double endX=(j+1)*width;
      double endY=0;
      //超过宽度以后
      if(j>=widthCounts){
        //一直在右边
        endX=widthCounts*width;
        endY=(j+1-widthCounts)*width;
      }
      //超过高度以后
      if(j>=heightCounts){
        startY=heightCounts*width;
        startX=(j+1-heightCounts)*width;
      }
      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), innerPaint);
    }
    //左斜线
    for(int j=0;j<heightCounts+widthCounts-1;j++){
      //从右左画
      //起点总是在最上边
      double startY=0;
      double startX=(widthCounts-(j+1))*width;
      //终点总是在最右边
      double endX=widthCounts*width;
      double endY=(j+1)*width;
      //超过高度以后
      if(j>=heightCounts){
        endX=endX-(j+1-heightCounts)*width;
        endY=endY-(j+1-heightCounts)*width;
      }
      //超过宽度以后
      if(j>=widthCounts-1){
        //一直在右边
        startX=0;
        startY=(j+1-widthCounts)*width;
      }
      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), innerPaint);
    }
  }


}

//以下是path_drawing实现虚线的部分
Path dashPath(
  Path source, {
  required CircularIntervalList<double> dashArray,
  DashOffset? dashOffset,
}) {
  dashOffset = dashOffset ?? const DashOffset.absolute(0.0);
  // TODO: Is there some way to determine how much of a path would be visible today?
  final Path dest = Path();
  for (final PathMetric metric in source.computeMetrics()) {
    double distance = dashOffset._calculate(metric.length);
    bool draw = true;
    while (distance < metric.length) {
      final double len = dashArray.next;
      if (draw) {
        dest.addPath(metric.extractPath(distance, distance + len), Offset.zero);
      }
      distance += len;
      draw = !draw;
    }
  }

  return dest;
}

enum _DashOffsetType { Absolute, Percentage }

class DashOffset {
  DashOffset.percentage(double percentage)
      : _rawVal = percentage.clamp(0.0, 1.0),
        _dashOffsetType = _DashOffsetType.Percentage;

  const DashOffset.absolute(double start)
      : _rawVal = start,
        _dashOffsetType = _DashOffsetType.Absolute;

  final double _rawVal;
  final _DashOffsetType _dashOffsetType;

  double _calculate(double length) {
    return _dashOffsetType == _DashOffsetType.Absolute
        ? _rawVal
        : length * _rawVal;
  }
}

class CircularIntervalList<T> {
  CircularIntervalList(this._vals);

  final List<T> _vals;
  int _idx = 0;

  T get next {
    if (_idx >= _vals.length) {
      _idx = 0;
    }
    return _vals[_idx++];
  }
}




import 'package:easy_white_board/model/db/drawing_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../common/config/storage_manager.dart';
import '../../objectbox.g.dart';

final drawingDataProvider=NotifierProvider<DrawingDataNotifier,List<DrawingData>>(
  DrawingDataNotifier.new,
  name: r'DrawingDataNotifier',
  dependencies: null
);

class DrawingDataNotifier extends Notifier<List<DrawingData>>{

  late Box<DrawingData> drawingDataStore;

  @override
  List<DrawingData> build() {
    drawingDataStore=StorageManager.objectbox.store.box<DrawingData>();
    return _loadSavedDrawing();
  }

  List<DrawingData> _loadSavedDrawing(){
    var list= drawingDataStore.getAll();
    _sortList(list);
    return list;
  }

  void _sortList(List<DrawingData> list){
    list.sort((d1,d2){
      return d2.createdTime.compareTo(d1.createdTime);
    });
  }

  void reloadData(){
    state=_loadSavedDrawing();
  }

  DrawingData? getDrawingData(int id){
    return drawingDataStore.get(id);
  }

  void saveDrawingData(DrawingData data){
    drawingDataStore.put(data);
    var list=state.toList();
    Map<String,DrawingData> idStrMap={};
    for (var element in list) {
         idStrMap[element.id.toString()]=element;
    }
    idStrMap[data.id.toString()]=data;
    var result=idStrMap.values.toList();
    _sortList(result);
    state=result;
  }

  void deleteData(int id){
    var list=state.toList();
    DrawingData? data;
    for (var element in list) {
      if(id.toString()==element.id.toString()){
        data=element;
      }
    }
    if(data!=null){
      list.remove(data);
    }
    drawingDataStore.remove(id);
    state=list;
  }
}
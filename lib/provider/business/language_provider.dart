import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../common/config/storage_manager.dart';
import '../../resource/shared_preferences_keys.dart';

/// 语言数据模型
class LanguageData {
  final String languageCode;
  final String countryCode;
  final String displayName;
  final String nativeName;
  final String flag;

  const LanguageData({
    required this.languageCode,
    required this.countryCode,
    required this.displayName,
    required this.nativeName,
    required this.flag,
  });

  Locale get locale => Locale(languageCode, countryCode);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LanguageData &&
          runtimeType == other.runtimeType &&
          languageCode == other.languageCode &&
          countryCode == other.countryCode;

  @override
  int get hashCode => languageCode.hashCode ^ countryCode.hashCode;
}

/// 支持的语言列表
class SupportedLanguages {
  static const List<LanguageData> languages = [
    LanguageData(
      languageCode: 'en',
      countryCode: 'US',
      displayName: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
    ),
    LanguageData(
      languageCode: 'zh',
      countryCode: 'CH',
      displayName: 'Chinese',
      nativeName: '中文',
      flag: '🇨🇳',
    ),
    LanguageData(
      languageCode: 'es',
      countryCode: 'ES',
      displayName: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
    ),
    LanguageData(
      languageCode: 'pt',
      countryCode: 'PT',
      displayName: 'Portuguese',
      nativeName: 'Português',
      flag: '🇵🇹',
    ),
    LanguageData(
      languageCode: 'ru',
      countryCode: 'RU',
      displayName: 'Russian',
      nativeName: 'Русский',
      flag: '🇷🇺',
    ),
    LanguageData(
      languageCode: 'ar',
      countryCode: 'SA',
      displayName: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
    ),
    LanguageData(
      languageCode: 'ja',
      countryCode: 'JP',
      displayName: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
    ),
    LanguageData(
      languageCode: 'ko',
      countryCode: 'KR',
      displayName: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
    ),
  ];

  static LanguageData? getLanguageByCode(String languageCode) {
    return languages.where((l) => l.languageCode == languageCode).firstOrNull;
  }
}

/// 语言设置Provider
final languageProvider = NotifierProvider<LanguageNotifier, LanguageData?>(
  LanguageNotifier.new,
  name: 'LanguageNotifier',
);

class LanguageNotifier extends Notifier<LanguageData?> {
  @override
  LanguageData? build() {
    return _loadSavedLanguage();
  }

  /// 从SharedPreferences加载保存的语言设置
  LanguageData? _loadSavedLanguage() {
    final savedLanguageCode = StorageManager.sharedPreferences
        .getString(SharedPreferencesKeys.selectedLanguage);

    if (savedLanguageCode != null) {
      return SupportedLanguages.getLanguageByCode(savedLanguageCode);
    }
    return null;
  }

  /// 更新语言设置
  Future<void> updateLanguage(LanguageData languageData) async {
    if (state != languageData) {
      // 保存到SharedPreferences
      await StorageManager.sharedPreferences.setString(
        SharedPreferencesKeys.selectedLanguage,
        languageData.languageCode,
      );

      // 更新状态
      state = languageData;
    }
  }

  /// 获取当前语言的Locale
  Locale? get currentLocale => state?.locale;
}

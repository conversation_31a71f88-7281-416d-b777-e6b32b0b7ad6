
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../page/drawing_board/widgets/bg/boarder_bg.dart';
import '../../page/drawing_board/widgets/bg/line_bg.dart';
import '../../page/drawing_board/widgets/bg/normal_bg.dart';
import '../../page/drawing_board/widgets/bg/tian_bg.dart';
import 'drawing_data_provider.dart';



class BgData{
  String bgType;
  Color bgColor;

  BgData({required this.bgType, required this.bgColor});

  factory BgData.fromJson(Map<String, dynamic> data){
    return BgData(
        bgType: data['bgType'] as String,
        bgColor: Color(data['bgColor'] as int)
    );
  }

  Map<String, dynamic> toJson(){
    return {
      "bgType":bgType,
      "bgColor":bgColor.toARGB32()
    };
  }

  static const String  defaultType = "NormalBg";
  static const Color  defaultColor = Colors.white;


  bool isSameBg(BgData bg){
    return bgType==bg.bgType && bgColor.toARGB32().toString()==bg.bgColor.toARGB32().toString();
  }
}


final bgProvider=NotifierProvider<BgNotifier,BgData>(
    BgNotifier.new,
    name: r'BgNotifier',
    dependencies: null
);


class BgNotifier extends Notifier<BgData>{


  @override
  BgData build() {
    return  BgData(bgColor: BgData.defaultColor, bgType: BgData.defaultType);
  }

  void loadSavedBg(int drawingDataId){
    var drawingData=ref.read(drawingDataProvider.notifier).getDrawingData(drawingDataId);
    if(drawingData!=null){
      BgData bg=BgData(bgType: drawingData.bgType, bgColor: Color(drawingData.bgColor));
      if(!state.isSameBg(bg)){
        state=bg;
      }
    }
  }

  void updateBg(String? runType,Color? bg){
    BgData bgData = BgData(bgType: state.bgType, bgColor: state.bgColor);
    if(runType!=null){
      bgData.bgType=runType;
    }
    if(bg!=null){
      bgData.bgColor=bg;
    }
    if(!state.isSameBg(bgData)){
      state=bgData;
    }
  }



  BoarderBg assembleBg(BgData bgData){
      BoarderBg bg;
      switch(bgData.bgType){
        case "LineBg":
          bg=LineBg(bgColor: bgData.bgColor,);
          break;
        case "TianBg":
          bg=TianBg(bgColor: bgData.bgColor,);
          break;
        default:
          bg=NormalBg(bgColor: bgData.bgColor, lineColor: null);
    }

    return bg;
  }
}




class TimeFormatHelper{

  static String parseSecondsToString(int duration){
    Duration d=Duration(seconds: duration);
    return parseDurationToString(d);
  }

  static String parseMileSecondsToString(int duration){
    Duration d=Duration(milliseconds: duration);
    return parseDurationToString(d);
  }

  static String parseDurationToString(Duration d){
    if(d.inHours>0){
      return "${d.inHours>9?'${d.inHours}':'0${d.inHours}'}:${d.inMinutes%60>9?'${d.inMinutes%60}':'0${d.inMinutes%60}'}:${d.inSeconds%60>9?'${d.inSeconds%60}':'0${d.inSeconds%60}'}";
    }else if(d.inMinutes>0){
      return "00:${d.inMinutes%60>9?'${d.inMinutes%60}':'0${d.inMinutes%60}'}:${d.inSeconds%60>9?'${d.inSeconds%60}':'0${d.inSeconds%60}'}";
    }else{
      return "00:00:${d.inSeconds%60>9?'${d.inSeconds%60}':'0${d.inSeconds%60}'}";
    }
  }


}
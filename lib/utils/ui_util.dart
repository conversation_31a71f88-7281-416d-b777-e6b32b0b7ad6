import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

void showToast(String msg) {
  _showToast(msg: msg);
}

showToastWithCustomBuilder(Widget widget) {
  _showToast(builder: (BuildContext context) => widget);
}

_showToast({String? msg, WidgetBuilder? builder}) {
  SmartDialog.showToast(msg ?? "", builder: builder);
}

showLoading({String? msg}) {
  _showLoading(msg: msg);
}

showLoadingWithCustomBuilder(Widget widget) {
  _showLoading(builder: (BuildContext context) => widget);
}

_showLoading({String? msg, WidgetBuilder? builder}) {
  SmartDialog.showLoading(
      msg: msg ?? "加载中", backDismiss: false, builder: builder);
}

dismissLoading() {
  SmartDialog.dismiss(status: SmartStatus.loading);
}

dismissDialog<T>({T? result}) {
  SmartDialog.dismiss(status: SmartStatus.custom, result: result);
}

dismissAttach<T>({T? result}) {
  SmartDialog.dismiss(status: SmartStatus.attach, result: result);
}

Future<T?> showSmartDialog<T>(
  Widget widget, {
  bool? backDismiss,
  bool? clickMaskDismiss,
  Alignment? alignment,
  SmartDialogController? controller,
}) async {
  return await SmartDialog.show<T>(
    controller: controller,
    builder: (BuildContext context) => widget,
    backDismiss: backDismiss ?? false,
    clickMaskDismiss: clickMaskDismiss ?? false,
    alignment: alignment ?? Alignment.center,
  );
}

Future<T?> showAttach<T>({
  required BuildContext targetContext,
  required Widget widget,
  bool? backDismiss,
  bool? clickMaskDismiss,
  Alignment? alignment,
  Color? maskColor,
  SmartDialogController? controller,
}) async {
  return await SmartDialog.showAttach<T>(
    targetContext: targetContext,
    controller: controller,
    builder: (BuildContext context) => widget,
    backDismiss: backDismiss ?? true,
    clickMaskDismiss: clickMaskDismiss ?? true,
    alignment: alignment,
  );
}

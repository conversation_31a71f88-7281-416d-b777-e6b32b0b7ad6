/// Flutter icons OwnIcons
/// Copyright (C) 2021 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  OwnIcons
///      fonts:
///       - asset: fonts/OwnIcons.ttf
///
/// 
/// * Entypo, Copyright (C) 2012 by <PERSON>
///         Author:    <PERSON>
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://www.entypo.com
/// * Material Design Icons, Copyright (C) Google, Inc
///         Author:    Google
///         License:   Apache 2.0 (https://www.apache.org/licenses/LICENSE-2.0)
///         Homepage:  https://design.google.com/icons/
/// * Linearicons Free, Copyright (C) Linearicons.com
///         Author:    Perxis
///         License:   CC BY-SA 4.0 (https://creativecommons.org/licenses/by-sa/4.0/)
///         Homepage:  https://linearicons.com
/// * Font Awesome 5, Copyright (C) 2016 by <PERSON>
///         Author:    <PERSON> Gandy
///         License:   SIL (https://github.com/FortAwesome/Font-Awesome/blob/master/LICENSE.txt)
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
/// * Font Awesome 4, Copyright (C) 2016 by Dave Gandy
///         Author:    Dave Gandy
///         License:   SIL ()
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
///
import 'package:flutter/widgets.dart';

class OwnIcons {
  OwnIcons._();

  static const _kFontFam = 'OwnIcons';
  static const String? _kFontPkg = null;

  static const IconData flow_line = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData text_fields = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData color_lens = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData photo_size_select_small = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData change_history = IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData check_box_outline_blank = IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData timeline = IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData highlight = IconData(0xe897, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData text_height = IconData(0xf034, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData move = IconData(0xf047, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_empty = IconData(0xf10c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData location_arrow = IconData(0xf124, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData eraser = IconData(0xf12d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_notch = IconData(0xf1ce, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData pencil_alt = IconData(0xf303, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData fill_drip = IconData(0xf576, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}

import 'dart:async';

import 'package:easy_white_board/common/config/build_config.dart';
import 'package:easy_white_board/provider/business/language_provider.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'app_initializer.dart';
import 'common/config/application.dart';
import 'common/config/router_manager.dart';
import 'common/localizations/global_localizations_delegate.dart';
import 'log/log.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() {
  runZonedGuarded(() async {
    await AppInitializer.initBeforeRunApp();
    runApp(
      const ProviderScope(child: MyApp()),
    );
    AppInitializer.initAfterRunApp();

    ///屏幕刷新率和显示率不一致时的优化，必须挪动到 runApp 之后
    GestureBinding.instance.resamplingEnabled = true;
    //设置状态栏的颜色透明
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light));
  }, (Object obj, StackTrace stack) {
    Logger.error(obj, stack);
  });
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _MyAppState();
  }
}

final RouteObserver<Route<dynamic>> routeObserver = RouteObserverHelper();

class _MyAppState extends ConsumerState<MyApp> {
  
  String _appName(BuildContext context) {
    return  BuildConfig.appName;
  }
 
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(375, 812));
    String initRoute = RouteName.drawingBoardPage;
    String appName = _appName(context);

    // 监听语言设置变化
    final currentLanguage = ref.watch(languageProvider);

    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          navigatorKey: MApplication.navigatorKey,
          debugShowCheckedModeBanner: false,
          onGenerateRoute: RouterManager.router.generator,
          initialRoute: initRoute,
          //设置当前选择的语言
          locale: currentLanguage?.locale,
          title: appName,
          navigatorObservers: [FlutterSmartDialog.observer, routeObserver],
          builder: FlutterSmartDialog.init(),
          localizationsDelegates: [
            //此处
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalLocalizationsDelegate.delegate,
          ],
          supportedLocales:
              GlobalLocalizationsDelegate.delegate.supportedLocales,
        );
      },
    );
  }
}

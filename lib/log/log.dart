
import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:stack_trace/stack_trace.dart';

import '../common/config/build_config.dart';
import 'log_util.dart';

class Logger{


  static _doLog(message){
    String flag="\x1B[32m 💙 =====INFO \x1B[0m";
    _doLogWithFlag(message, flag);
  }

  static _doLogWithFlag(message,String flag){
    if(!BuildConfig.isProduction){
      var current = Trace.current(3);
      var frame = current.frames[0];
      var timeStr=formatDate(DateTime.now(), [HH, ':', nn, ':', ss,':',SSS]);
      LogUtil.log("$timeStr $frame \n $flag:$message");
    }
  }


  static _doLogWithStacktrace(dynamic message,StackTrace? stacktrace){
    String flag="\x1B[31m ❤️ ERROR \x1B[0m";
    if(stacktrace!=null){
      var trace = Trace.from(stacktrace);
      String msgStr="$message \n error stacktrace: \n $trace";
      _doLogWithFlag(msgStr, flag);
    }else {
      _doLogWithFlag(message, flag);
    }
  }

  static info(message){
    _doLog(message);
  }



  static void error(dynamic message, [StackTrace? stacktrace]) {
    _doLogWithStacktrace(message,stacktrace);
  }

}
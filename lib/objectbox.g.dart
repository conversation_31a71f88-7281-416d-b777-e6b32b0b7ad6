// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again:
// With a Flutter package, run `flutter pub run build_runner build`.
// With a Dart package, run `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart';
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'model/db/content_painter_style_data.dart';
import 'model/db/drawing_data.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <ModelEntity>[
  ModelEntity(
      id: const IdUid(1, 3168816409222465835),
      name: 'Drawing<PERSON><PERSON>',
      lastPropertyId: const IdUid(9, 1310277911093120247),
      flags: 0,
      properties: <ModelProperty>[
        ModelProperty(
            id: const IdUid(1, 3248917397172267141),
            name: 'id',
            type: 6,
            flags: 1),
        ModelProperty(
            id: const IdUid(2, 3444736689392301363),
            name: 'content',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(3, 5628178781050468104),
            name: 'createdTime',
            type: 6,
            flags: 0),
        ModelProperty(
            id: const IdUid(4, 4009566769959261753),
            name: 'updatedTime',
            type: 6,
            flags: 0),
        ModelProperty(
            id: const IdUid(5, 8506780234699037278),
            name: 'username',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(6, 4817086167245381091),
            name: 'name',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(7, 8913292625502423385),
            name: 'imageStr',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(8, 1180616505519074623),
            name: 'bgType',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(9, 1310277911093120247),
            name: 'bgColor',
            type: 6,
            flags: 0)
      ],
      relations: <ModelRelation>[],
      backlinks: <ModelBacklink>[]),
  ModelEntity(
      id: const IdUid(2, 7923644426347554807),
      name: 'ContentPainterStyleData',
      lastPropertyId: const IdUid(9, 4111272093478509966),
      flags: 0,
      properties: <ModelProperty>[
        ModelProperty(
            id: const IdUid(1, 7952227425841839690),
            name: 'id',
            type: 6,
            flags: 1),
        ModelProperty(
            id: const IdUid(3, 6115189940162821886),
            name: 'color',
            type: 6,
            flags: 0),
        ModelProperty(
            id: const IdUid(4, 6792001587150219076),
            name: 'width',
            type: 8,
            flags: 0),
        ModelProperty(
            id: const IdUid(5, 8407428040376999732),
            name: 'createdTime',
            type: 6,
            flags: 0),
        ModelProperty(
            id: const IdUid(6, 963447774976067750),
            name: 'updatedTime',
            type: 6,
            flags: 0),
        ModelProperty(
            id: const IdUid(8, 1278289240323046436),
            name: 'runType',
            type: 9,
            flags: 0),
        ModelProperty(
            id: const IdUid(9, 4111272093478509966),
            name: 'extraConfig',
            type: 9,
            flags: 0)
      ],
      relations: <ModelRelation>[],
      backlinks: <ModelBacklink>[])
];

/// Open an ObjectBox store with the model declared in this file.
Future<Store> openStore(
        {String? directory,
        int? maxDBSizeInKB,
        int? fileMode,
        int? maxReaders,
        bool queriesCaseSensitiveDefault = true,
        String? macosApplicationGroup}) async =>
    Store(getObjectBoxModel(),
        directory: directory ?? (await defaultStoreDirectory()).path,
        maxDBSizeInKB: maxDBSizeInKB,
        fileMode: fileMode,
        maxReaders: maxReaders,
        queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
        macosApplicationGroup: macosApplicationGroup);

/// ObjectBox model definition, pass it to [Store] - Store(getObjectBoxModel())
ModelDefinition getObjectBoxModel() {
  final model = ModelInfo(
      entities: _entities,
      lastEntityId: const IdUid(3, 2056482517565277185),
      lastIndexId: const IdUid(0, 0),
      lastRelationId: const IdUid(0, 0),
      lastSequenceId: const IdUid(0, 0),
      retiredEntityUids: const [2056482517565277185],
      retiredIndexUids: const [],
      retiredPropertyUids: const [
        1592068045215174861,
        5521974627439720367,
        415184518456637046,
        6278861159988986583,
        8633165264391645552,
        7332150870967758632,
        5481044803742573392,
        3827530453244410746,
        3072165241536296058,
        3568557732534537502,
        135021378203597156
      ],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, EntityDefinition>{
    DrawingData: EntityDefinition<DrawingData>(
        model: _entities[0],
        toOneRelations: (DrawingData object) => [],
        toManyRelations: (DrawingData object) => {},
        getId: (DrawingData object) => object.id,
        setId: (DrawingData object, int id) {
          object.id = id;
        },
        objectToFB: (DrawingData object, fb.Builder fbb) {
          final contentOffset = fbb.writeString(object.content);
          final usernameOffset = object.username == null
              ? null
              : fbb.writeString(object.username!);
          final nameOffset =
              object.name == null ? null : fbb.writeString(object.name!);
          final imageStrOffset = object.imageStr == null
              ? null
              : fbb.writeString(object.imageStr!);
          final bgTypeOffset = fbb.writeString(object.bgType);
          fbb.startTable(10);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addOffset(1, contentOffset);
          fbb.addInt64(2, object.createdTime);
          fbb.addInt64(3, object.updatedTime);
          fbb.addOffset(4, usernameOffset);
          fbb.addOffset(5, nameOffset);
          fbb.addOffset(6, imageStrOffset);
          fbb.addOffset(7, bgTypeOffset);
          fbb.addInt64(8, object.bgColor);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);

          final object = DrawingData(
              id: const fb.Int64Reader()
                  .vTableGetNullable(buffer, rootOffset, 4),
              content: const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, ''),
              createdTime:
                  const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
              updatedTime:
                  const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0),
              username: const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 12),
              name: const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 14),
              imageStr: const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 16),
              bgType: const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 18, ''),
              bgColor:
                  const fb.Int64Reader().vTableGet(buffer, rootOffset, 20, 0));

          return object;
        }),
    ContentPainterStyleData: EntityDefinition<ContentPainterStyleData>(
        model: _entities[1],
        toOneRelations: (ContentPainterStyleData object) => [],
        toManyRelations: (ContentPainterStyleData object) => {},
        getId: (ContentPainterStyleData object) => object.id,
        setId: (ContentPainterStyleData object, int id) {
          object.id = id;
        },
        objectToFB: (ContentPainterStyleData object, fb.Builder fbb) {
          final runTypeOffset = fbb.writeString(object.runType);
          final extraConfigOffset = object.extraConfig == null
              ? null
              : fbb.writeString(object.extraConfig!);
          fbb.startTable(10);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(2, object.color);
          fbb.addFloat64(3, object.width);
          fbb.addInt64(4, object.createdTime);
          fbb.addInt64(5, object.updatedTime);
          fbb.addOffset(7, runTypeOffset);
          fbb.addOffset(8, extraConfigOffset);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);

          final object = ContentPainterStyleData(
              id: const fb.Int64Reader()
                  .vTableGetNullable(buffer, rootOffset, 4),
              runType: const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 18, ''),
              color: const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
              width:
                  const fb.Float64Reader().vTableGet(buffer, rootOffset, 10, 0),
              extraConfig: const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 20),
              createdTime:
                  const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
              updatedTime:
                  const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0));

          return object;
        })
  };

  return ModelDefinition(model, bindings);
}

/// [DrawingData] entity fields to define ObjectBox queries.
class DrawingData_ {
  /// see [DrawingData.id]
  static final id =
      QueryIntegerProperty<DrawingData>(_entities[0].properties[0]);

  /// see [DrawingData.content]
  static final content =
      QueryStringProperty<DrawingData>(_entities[0].properties[1]);

  /// see [DrawingData.createdTime]
  static final createdTime =
      QueryIntegerProperty<DrawingData>(_entities[0].properties[2]);

  /// see [DrawingData.updatedTime]
  static final updatedTime =
      QueryIntegerProperty<DrawingData>(_entities[0].properties[3]);

  /// see [DrawingData.username]
  static final username =
      QueryStringProperty<DrawingData>(_entities[0].properties[4]);

  /// see [DrawingData.name]
  static final name =
      QueryStringProperty<DrawingData>(_entities[0].properties[5]);

  /// see [DrawingData.imageStr]
  static final imageStr =
      QueryStringProperty<DrawingData>(_entities[0].properties[6]);

  /// see [DrawingData.bgType]
  static final bgType =
      QueryStringProperty<DrawingData>(_entities[0].properties[7]);

  /// see [DrawingData.bgColor]
  static final bgColor =
      QueryIntegerProperty<DrawingData>(_entities[0].properties[8]);
}

/// [ContentPainterStyleData] entity fields to define ObjectBox queries.
class ContentPainterStyleData_ {
  /// see [ContentPainterStyleData.id]
  static final id =
      QueryIntegerProperty<ContentPainterStyleData>(_entities[1].properties[0]);

  /// see [ContentPainterStyleData.color]
  static final color =
      QueryIntegerProperty<ContentPainterStyleData>(_entities[1].properties[1]);

  /// see [ContentPainterStyleData.width]
  static final width =
      QueryDoubleProperty<ContentPainterStyleData>(_entities[1].properties[2]);

  /// see [ContentPainterStyleData.createdTime]
  static final createdTime =
      QueryIntegerProperty<ContentPainterStyleData>(_entities[1].properties[3]);

  /// see [ContentPainterStyleData.updatedTime]
  static final updatedTime =
      QueryIntegerProperty<ContentPainterStyleData>(_entities[1].properties[4]);

  /// see [ContentPainterStyleData.runType]
  static final runType =
      QueryStringProperty<ContentPainterStyleData>(_entities[1].properties[5]);

  /// see [ContentPainterStyleData.extraConfig]
  static final extraConfig =
      QueryStringProperty<ContentPainterStyleData>(_entities[1].properties[6]);
}

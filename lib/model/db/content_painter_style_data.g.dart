// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_painter_style_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContentPainterStyleData _$ContentPainterStyleDataFromJson(
        Map<String, dynamic> json) =>
    ContentPainterStyleData(
      id: json['id'] as int?,
      runType: json['runType'] as String,
      color: json['color'] as int?,
      width: (json['width'] as num?)?.toDouble() ?? PainterStyle.defaultWidth,
      extraConfig: json['extraConfig'] as String?,
      createdTime: json['createdTime'] as int,
      updatedTime: json['updatedTime'] as int,
    );

Map<String, dynamic> _$ContentPainterStyleDataToJson(
        ContentPainterStyleData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'runType': instance.runType,
      'color': instance.color,
      'width': instance.width,
      'createdTime': instance.createdTime,
      'updatedTime': instance.updatedTime,
      'extraConfig': instance.extraConfig,
    };

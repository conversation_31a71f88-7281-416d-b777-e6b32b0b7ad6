// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'drawing_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DrawingData _$DrawingDataFromJson(Map<String, dynamic> json) => DrawingData(
      id: json['id'] as int?,
      content: json['content'] as String,
      createdTime: json['createdTime'] as int,
      updatedTime: json['updatedTime'] as int,
      username: json['username'] as String?,
      name: json['name'] as String?,
      imageStr: json['imageStr'] as String?,
      bgType: json['bgType'] as String? ?? BgData.defaultType,
      bgColor: json['bgColor'] as int?,
    );

Map<String, dynamic> _$DrawingDataToJson(DrawingData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'content': instance.content,
      'imageStr': instance.imageStr,
      'createdTime': instance.createdTime,
      'updatedTime': instance.updatedTime,
      'username': instance.username,
      'bgType': instance.bgType,
      'bgColor': instance.bgColor,
    };

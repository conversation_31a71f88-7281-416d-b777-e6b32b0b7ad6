
import 'package:fluro/fluro.dart';
import 'package:flutter/cupertino.dart';

import 'common/config/router_manager.dart';
import 'common/config/storage_manager.dart';

class AppInitializer {
  static Future initBeforeRunApp() async {
    WidgetsFlutterBinding.ensureInitialized();

    _initRouter();

    await StorageManager.initSp();

    await StorageManager.initDb();
  
  }

  static void initAfterRunApp(){
    ///获取版本信息
     _initAppInfo();
  }



  static void _initRouter(){
    final router = FluroRouter();
    Routes.configureRoutes(router);
    // 这里设置项目环境
    RouterManager.router = router;
  }

  static void _initAppInfo(){

  }


}
# 语言设置使用指南 / Language Settings Guide

## 功能概述 / Feature Overview

白板大师应用现在支持8种语言的动态切换，用户可以在应用内随时更改界面语言，无需重启应用。

The White Board Master app now supports dynamic switching between 8 languages. Users can change the interface language within the app at any time without restarting.

## 如何使用 / How to Use

### 1. 打开语言设置 / Open Language Settings

在主页右上角，您会看到一个显示当前语言国旗的按钮（例如：🇺🇸 🇨🇳）。点击这个按钮即可打开语言选择对话框。

On the home page's top right corner, you'll see a button displaying the current language flag (e.g., 🇺🇸 🇨🇳). Click this button to open the language selection dialog.

### 2. 选择语言 / Select Language

在语言选择对话框中，您可以看到：
- 当前选择的语言（高亮显示）
- 所有支持的语言列表，包括：
  - 国旗图标
  - 本地语言名称
  - 英文名称

In the language selection dialog, you can see:
- Currently selected language (highlighted)
- List of all supported languages, including:
  - Flag icon
  - Native language name
  - English name

### 3. 应用语言更改 / Apply Language Change

点击任何语言选项后：
- 应用会立即切换到选择的语言
- 对话框会自动关闭
- 所有界面文字会更新为新语言
- 语言设置会自动保存

After clicking any language option:
- The app will immediately switch to the selected language
- The dialog will close automatically
- All interface text will update to the new language
- Language settings will be saved automatically

## 支持的语言 / Supported Languages

| 语言 | 本地名称 | 国旗 |
|------|----------|------|
| English | English | 🇺🇸 |
| 中文 | 中文 | 🇨🇳 |
| Español | Español | 🇪🇸 |
| Português | Português | 🇵🇹 |
| Русский | Русский | 🇷🇺 |
| العربية | العربية | 🇸🇦 |
| 日本語 | 日本語 | 🇯🇵 |
| 한국어 | 한국어 | 🇰🇷 |

## 技术特性 / Technical Features

### 🔄 动态切换 / Dynamic Switching
- 无需重启应用
- 实时界面更新
- 状态持久化保存

### 💾 自动保存 / Auto Save
- 语言选择自动保存到本地存储
- 下次启动应用时自动恢复上次选择的语言

### 🌐 智能回退 / Smart Fallback
- 如果设备语言不在支持列表中，自动使用英语
- 确保应用在任何设备上都能正常显示

### 🎨 美观界面 / Beautiful Interface
- 直观的国旗图标显示
- 清晰的语言名称展示
- 友好的用户交互体验

## 开发者信息 / Developer Information

### 文件结构 / File Structure
```
lib/
├── provider/business/language_provider.dart    # 语言状态管理
├── widgets/dialog/language_selection_dialog.dart  # 语言选择对话框
├── page/home/<USER>
└── common/localizations/                      # 多语言资源文件
    ├── global_string.dart                     # 英语（默认）
    ├── global_string_zh.dart                  # 中文
    ├── global_string_es.dart                  # 西班牙语
    ├── global_string_pt.dart                  # 葡萄牙语
    ├── global_string_ru.dart                  # 俄语
    ├── global_string_ar.dart                  # 阿拉伯语
    ├── global_string_ja.dart                  # 日语
    └── global_string_ko.dart                  # 韩语
```

### 核心组件 / Core Components
- **LanguageProvider**: 使用Riverpod管理语言状态
- **LanguageSelectionDialog**: 语言选择对话框组件
- **SharedPreferences**: 持久化存储语言设置

## 故障排除 / Troubleshooting

### 问题：语言切换后部分文字没有更新
**解决方案**：确保所有使用的字符串都通过 `context.string.xxx` 方式调用

### 问题：应用重启后语言设置丢失
**解决方案**：检查SharedPreferences权限，确保应用有存储权限

### 问题：某些语言显示乱码
**解决方案**：确保设备支持对应语言的字体渲染

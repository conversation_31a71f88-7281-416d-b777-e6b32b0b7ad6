# 多语言支持文档 / Multilingual Support Documentation

## 概述 / Overview

白板大师应用现在支持以下8种语言：
The White Board Master app now supports the following 8 languages:

- 🇺🇸 English (英语)
- 🇨🇳 中文 (Chinese)
- 🇪🇸 Español (西班牙语)
- 🇵🇹 Português (葡萄牙语)
- 🇷🇺 Русский (俄语)
- 🇸🇦 العربية (阿拉伯语 - 沙特)
- 🇯🇵 日本語 (日语)
- 🇰🇷 한국어 (韩语)

## 文件结构 / File Structure

多语言相关文件位于 `lib/common/localizations/` 目录下：
Multilingual files are located in the `lib/common/localizations/` directory:

```
lib/common/localizations/
├── global_localizations.dart          # 多语言管理器
├── global_localizations_delegate.dart # 多语言代理
├── global_string.dart                 # 英语 (默认)
├── global_string_zh.dart              # 中文
├── global_string_es.dart              # 西班牙语
├── global_string_pt.dart              # 葡萄牙语
├── global_string_ru.dart              # 俄语
├── global_string_ar.dart              # 阿拉伯语
├── global_string_ja.dart              # 日语
└── global_string_ko.dart              # 韩语
```

## 支持的语言代码 / Supported Language Codes

| 语言 / Language | 代码 / Code | 地区 / Region |
|-----------------|-------------|---------------|
| English         | en          | US            |
| 中文            | zh          | CH            |
| Español         | es          | ES            |
| Português       | pt          | PT            |
| Русский         | ru          | RU            |
| العربية         | ar          | SA            |
| 日本語          | ja          | JP            |
| 한국어          | ko          | KR            |

## 使用方法 / Usage

### 用户使用 / User Usage

用户可以通过以下方式切换语言：
Users can switch languages in the following ways:

1. **在主页切换语言 / Switch Language on Home Page**
   - 在主页右上角点击语言标志（如 🇺🇸 🇨🇳）
   - 选择想要的语言
   - 应用会立即切换到选择的语言

2. **自动语言检测 / Automatic Language Detection**
   - 应用会根据设备系统语言自动选择对应语言
   - 如果系统语言不在支持列表中，会默认使用英语

### 开发者使用 / Developer Usage

在Flutter代码中使用多语言字符串：
To use multilingual strings in Flutter code:

```dart
import 'package:easy_white_board/common/extension/build_context_ext.dart';

// 在Widget中使用
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Text(context.string.appName); // 显示应用名称
  }
}

// 或者直接使用
import 'package:easy_white_board/common/localizations/global_localizations.dart';

final strings = GlobalLocalizations.i18n(context);
print(strings.cancel); // 显示"取消"或"Cancel"等
```

## 可用的字符串 / Available Strings

当前支持的多语言字符串包括：
Currently supported multilingual strings include:

- `appName` - 应用名称
- `cancel` - 取消
- `confirm` - 确认
- `delete` - 删除
- `untitled` - 未命名
- `welcomeToUse` - 欢迎使用
- `confirmToDelete` - 确认删除
- `renameDrawing` - 重命名绘图
- `nameCouldNotEmpty` - 名称不能为空
- `onSaving` - 保存中
- `handWriting` - 手写
- `strokeWidth` - 笔画宽度
- `languageSettings` - 语言设置
- `selectLanguage` - 选择语言
- `currentLanguage` - 当前语言

## 添加新语言 / Adding New Languages

要添加新语言支持：
To add support for a new language:

1. 创建新的语言文件 `global_string_[language_code].dart`
2. 继承 `GlobalString` 类并重写所有字符串属性
3. 在 `global_localizations.dart` 中添加导入和映射
4. 在 `global_localizations_delegate.dart` 中添加支持的语言代码

## 回退机制 / Fallback Mechanism

如果用户设备的语言不在支持列表中，应用将自动回退到英语。
If the user's device language is not in the supported list, the app will automatically fall back to English.

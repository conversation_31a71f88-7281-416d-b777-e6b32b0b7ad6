plugins {
         id "com.android.application"
         id "kotlin-android"
         id "dev.flutter.flutter-gradle-plugin"
     }


def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}



android {
    namespace "com.v.easy_white_board.easy_white_board"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.v.easy_white_board.easy_white_board"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 26
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        manifestPlaceholders = [
                CHANNEL_NAME: "11000",
        ]
        setProperty("archivesBaseName", "画板${versionCode}-${new Date().format('yyyyMMdd')}")

        ndk { abiFilters 'armeabi-v7a','arm64-v8a', 'x86_64' }
    }


    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        debug {
            keyAlias keystoreProperties['keyAlias-debug']
            keyPassword keystoreProperties['keyPassword-debug']
            storeFile file(keystoreProperties['storeFile-debug'])
            storePassword keystoreProperties['storePassword-debug']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
    }


//    flavorDimensions 'channel'
//    productFlavors {
//        '11000'{
//            dimension 'channel'
//            manifestPlaceholders = [
//                    CHANNEL_NAME: name,
//            ]
//        }
//    }
}

flutter {
    source '../..'
}

dependencies {

}

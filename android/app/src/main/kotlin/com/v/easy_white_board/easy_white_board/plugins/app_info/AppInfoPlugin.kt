package com.v.easy_white_board.easy_white_board.plugins.app_info

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PermissionInfo
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.v.easy_white_board.easy_white_board.MainActivity
import com.v.easy_white_board.easy_white_board.app.App
import io.flutter.app.FlutterApplication
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class AppInfoPlugin(
    private val activity: Activity
    ) : MethodChannel.MethodCallHandler {


    companion object{
        /** Plugin registration.  */
        fun registerWith(flutterEngine: FlutterEngine, context: Activity) {
            val channel = MethodChannel(
                flutterEngine.dartExecutor,
                "plugins.${App.pluginPre}/appInfoPlugin"
            )
            channel.setMethodCallHandler(
                AppInfoPlugin(context)
            )
        }
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        if (call.method == "getApplicationInfo") {
            val applicationInfo = getApplicationInfo()
            result.success(applicationInfo)
        }else if(call.method=="getAppName"){
            val appName = getAppName()
            result.success(appName)
        }else if(call.method=="backToHome"){
            backToHome()
        }else if(call.method=="backToForeground"){
            backToForeground();
        }
    }

    fun getAppName(): String? {
        val context = App.getApp().baseContext
        try {
            val packageManager = context.packageManager
            return packageManager.getApplicationLabel(context.applicationInfo).toString()
        } catch (e: Throwable) {
        }
        return null
    }


    private fun  backToForeground(){
        val callIntentService: Intent = Intent(
            activity,
            MainActivity::class.java
        )
        callIntentService.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        activity.startActivity(callIntentService)
    }

    private fun backToHome() {
        val intent = Intent() // 创建Intent对象

        intent.setAction(Intent.ACTION_MAIN) // 设置Intent动作

        intent.addCategory(Intent.CATEGORY_HOME) // 设置Intent种类

        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK) //标记

        activity.startActivity(intent)
    }




    private fun getAppName(pID: Int): String {
        var processName  = ""
        val am = activity.getSystemService(FlutterApplication.ACTIVITY_SERVICE) as ActivityManager
        val l: List<*> = am.runningAppProcesses
        val i = l.iterator()
        while (i.hasNext()) {
            val info = i.next() as ActivityManager.RunningAppProcessInfo
            try {
                if (info.pid == pID) {
                    processName = info.processName
                    return processName
                }
            } catch (e: Exception) {
                // Log.d("Process", "Error>> :"+ e.toString());
            }
        }
        return processName
    }


    private fun getApplicationInfo(): Map<String,String> {
        val applicationInfo = activity.packageManager.getApplicationInfo(activity.packageName, PackageManager.GET_META_DATA)
        var channelName = applicationInfo.metaData.getString("CHANNEL_NAME")?:""
        var packageName = activity.packageName
        var version:String= getVersionName(activity)?:""
        var buildNumber=getVersionCode(activity)
        var sdkVersion=Build.VERSION.SDK_INT;
        return mutableMapOf("channelName" to channelName,"packageName" to packageName,"version" to version,"buildNumber" to buildNumber.toString(),"sdkVersion" to sdkVersion.toString())
    }


    private fun getVersionName(context: Context): String? {
        try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(
                context.packageName, 0
            )
            return packageInfo.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return ""
    }

    private fun getVersionCode(context: Context): Long {
        try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(
                context.packageName, 0
            )
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                packageInfo.versionCode.toLong()
            }
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return 0
    }



}
storePassword=FC#aAwvg92kYu2FJY%kX
keyPassword=FC#aAwvg92kYu2FJY%kX
keyAlias=key
storeFile=Users/lenovo/key.jks


storePassword-debug=FC#aAwvg92kYu2FJY%kX
keyPassword-debug=FC#aAwvg92kYu2FJY%kX
keyAlias-debug=key
#storeFile-debug=../key.jks
storeFile-debug=Users/lenovo/key.jks


#jarsigner -verbose -keystore D:\Workspase\AndriodStudio\message_recorder\android\app\Users\lenovo\key.jks -signedjar 1.0.0_20200511_release_normal_signed_100_jiagu.apk 1.0.0_20200511_release_normal_sign_100_jiagu.apk key

# keytool -genkeypair -v -keystore key -alias key -keyalg RSA -validity 36500 -keypass 'rdfg**sdf**%##@Asd1' -storepass 'rdfg**sdf**%##@Asd1' -dname "CN=fanle, OU=xx, O=xx, L=xx, ST=xx, C=xx"

#keytool -importkeystore -srckeystore key.jks -destkeystore key.keystore -deststoretype pkcs12

#查看key的sha1
keytool -v -list -keystore googlesignin.jks